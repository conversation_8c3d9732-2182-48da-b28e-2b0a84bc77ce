<template>
  <div class="dialog-content">
    <div class="content-top">
      <!-- <div>
        <div class="work-order-label">报警ID :</div>
        <div class="work-order-value">{{ alarmId }}</div>
      </div> -->
      <div>
        <!-- 来电号码部分已注释
        <div class="work-order-label">来电号码 :</div>
        <div class="work-order-value">
          <el-input v-model="formInline.needPhone" :readonly="dealType === 'deal'" placeholder="请输入来电号码" @blur="handleNeedPhoneBlur"></el-input>
        </div>
        -->
        <div class="right-tips"><span class="v-img"></span>{{ workOrderDetail.taskWorkHint }}</div>
        <div style="display: flex; align-items: center; margin-left: auto; font-size: 16px">
          <div class="work-order-label">开单时间 :</div>
          <div class="work-order-value" style="color: #3562db; font-weight: normal">{{ kd_time }}</div>
          <!-- <el-button style="margin-left: 10px" plain type="primary" @click="placeOrder()"><i class="el-icon-plus"></i> 再建一单</el-button> -->
        </div>
      </div>
    </div>
    <div class="content-footer">
      <el-row :gutter="20" :type="$store.state.settings.mode == 'mobile' ? '' : 'flex'" style="align-items: stretch">
        <el-col :md="7">
          <ContentCard title="服务事项：" :required="isFieldRequired('itemTypeCode')" :cstyle="{ height: '100%', overflow: 'hidden' }">
            <span slot="title-right" :title="selectService" class="select-servive">{{ selectService }}</span>
            <div slot="content" class="footer-left">
              <el-input v-model="filterText" placeholder="请输入关键词" style="margin: 10px 0"></el-input>
              <!-- 服务事项TOP5标签 -->
              <div v-if="itemTop5List.length > 0" class="item-top5-tags">
                <el-tag
                  v-for="(item, index) in itemTop5List"
                  :key="index"
                  size="small"
                  :type="selectedTop5Index === index ? 'primary' : 'info'"
                  :effect="selectedTop5Index === index ? 'light' : 'plain'"
                  class="top5-tag"
                  @click="selectTop5Item(item, index)"
                >
                  {{ item.itemServiceName }}
                </el-tag>
              </div>
              <el-tree ref="tree" class="tree" :filter-node-method="filterNode" node-key="id" :data="itemTreeData" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
            </div>
          </ContentCard>
        </el-col>
        <el-col :md="17" style="padding-left: 0">
          <ContentCard title="工单信息" :cstyle="{ height: '100%', 'overflow-y': 'auto' }">
            <div slot="content" class="footer-right">
              <el-form ref="formInline" class="form-data" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium" :validate-on-rule-change="false">
                <div class="formRow margin-bottom-none service-time-row">
                  <el-form-item label="服务时间：" prop="appointmentType" class="appointment-type">
                    <el-radio v-model="formInline.appointmentType" label="0">立刻</el-radio>
                    <el-radio v-model="formInline.appointmentType" label="1">预约</el-radio>
                  </el-form-item>
                  <el-form-item v-if="formInline.appointmentType === '1'" prop="appointmentDate" class="appointment-date">
                    <el-date-picker
                      v-model="formInline.appointmentDate"
                      :picker-options="pickerOptions"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      popper-class="timePicker"
                      type="datetime"
                      placeholder="选择日期时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </div>
                <div class="formRow margin-bottom-none" style="width: 100%">
                  <el-form-item style="width: 70%; flex: auto" label="紧急程度：" prop="urgencyDegree">
                    <el-radio
                      v-for="item in urgencyOptions"
                      :key="item.dictValue"
                      v-model="formInline.urgencyDegree"
                      :label="item.dictValue"
                      @change="urgencyChange(item.dictValue)"
                    >
                      {{ item.dictLabel }}
                    </el-radio>
                  </el-form-item>
                  <el-form-item v-if="formInline.urgencyDegree !== '2'" style="width: auto; flex: auto" label="" prop="noticeBtn">
                    <el-checkbox v-model="formInline.noticeBtn" @change="noticeBtnChange">通知</el-checkbox>
                  </el-form-item>
                </div>
                <div class="formRow margin-bottom-none" style="width: 100%">
                  <el-form-item label="申报属性：" prop="typeSources" show-message>
                    <el-radio v-for="item in typeSourcesOptions" :key="item.dictValue" v-model="formInline.typeSources" :label="item.dictValue">
                      {{ item.dictLabel }}
                    </el-radio>
                  </el-form-item>
                </div>
                <table v-if="selectNoticePeopleRow.length" class="maint-table" style="table-layout: fixed">
                  <tbody>
                    <tr>
                      <td style="width: 42%">联系人</td>
                      <td style="width: 42%">人员电话</td>
                      <td style="width: 16%">操作</td>
                    </tr>
                    <tr v-for="(item, index) in selectNoticePeopleRow" :key="index">
                      <td>
                        <div :title="item.name" class="one-line">{{ item.name }}</div>
                      </td>
                      <td>
                        <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                      </td>
                      <td>
                        <div class="one-line scope-del" @click="noticePeopleDel(item.id)">删除</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div v-if="!isInspectionWorkOrder" class="formRow">
                  <el-form-item label="要求完工时间：" prop="requireAccomplishDate">
                    <el-date-picker
                      v-model="formInline.requireAccomplishDate"
                      :picker-options="pickerOptions"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      popper-class="timePicker"
                      type="datetime"
                      placeholder="选择完工时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </div>
                <div class="formRow">
                  <el-form-item label="服务地点：" prop="localtionName" class="location-form-item">
                    <div class="location-select-container">
                      <el-select v-model="formInline.localtion" filterable placeholder="请选择服务地点" @change="handleLocationChange">
                        <el-option v-for="item in locationOptions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                      </el-select>
                      <el-button type="primary" size="mini" @click="getLocaltion">选择</el-button>
                    </div>
                  </el-form-item>
                </div>
                <div class="formRow" style="width: 45%">
                  <el-form-item label="所属科室：" prop="sourcesDept" class="dept-form-item">
                    <div class="dept-select-container">
                      <el-select v-model="formInline.sourcesDept" filterable placeholder="请选择所属科室">
                        <el-option v-for="item in sourcesDeptOptions" :key="item.value" :label="item.label" :value="item.value + '_' + item.label"> </el-option>
                      </el-select>
                      <el-button type="primary" size="mini" @click="openDeptSelectDialog('sourcesDept')">选择</el-button>
                    </div>
                  </el-form-item>
                </div>
                <div class="formRow" style="width: 100%; display: flex; justify-content: space-between">
                  <el-form-item prop="callerJobNum" style="width: 30%; margin-right: 0">
                    <span slot="label">工号：</span>
                    <el-input v-model.trim="formInline.callerJobNum" placeholder="请输入工号"></el-input>
                  </el-form-item>
                  <el-form-item label="联系人：" prop="callerName" style="width: 30%; margin-right: 0">
                    <el-input v-model.trim="formInline.callerName" placeholder="请输入联系人"></el-input>
                  </el-form-item>
                  <el-form-item class="phone-item" prop="sourcesPhone" style="width: 30%">
                    <span slot="label">电话：</span>
                    <el-input v-model.trim="formInline.sourcesPhone" placeholder="请输入电话" @blur="handlePhoneBlur"></el-input>
                  </el-form-item>
                </div>
                <div class="formRow desc-attachment-row" style="width: 100%">
                  <el-form-item class="description-item" label="申报描述：" prop="questionDescription">
                    <el-input
                      v-model="formInline.questionDescription"
                      :rows="3"
                      maxlength="500"
                      placeholder="请您输入申报描述，字数限制500字以内"
                      type="textarea"
                      onkeyup="if(value.length>500)value=value.slice(0,500)"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                  <div class="attachment-container">
                    <div class="attachment-content">
                      <div v-if="workOrderDetail.listAttUrl && workOrderDetail.listAttUrl.length > 0" class="image-section">
                        <div class="section-row">
                          <div class="attachment-label">图片：</div>
                          <div class="image-container">
                            <el-image
                              v-for="(img, index) in workOrderDetail.listAttUrl"
                              :key="'img_' + index"
                              class="attachment-image"
                              :src="$tools.imgUrlTranslation(img)"
                              fit="cover"
                              :preview-src-list="workOrderDetail.listAttUrl.map((url) => $tools.imgUrlTranslation(url))"
                            >
                            </el-image>
                          </div>
                        </div>
                      </div>
                      <div v-if="workOrderDetail.audioPath" class="audio-container">
                        <div class="section-row">
                          <div class="attachment-label">录音：</div>
                          <div class="audio-file">
                            <span class="file-name">{{ getFileName(workOrderDetail.audioPath) }}</span>
                            <div class="audio-actions">
                              <i class="el-icon-video-play action-icon" title="播放" @click="playAudio(workOrderDetail.audioPath)"></i>
                              <i class="el-icon-download action-icon" title="下载" @click="downloadAudio(workOrderDetail.audioPath)"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="formRow dept-row" style="width: 100%">
                  <el-form-item style="flex: 2; min-width: 60%" label="服务部门：" prop="designateDeptCode" class="dept-form-item">
                    <div class="dept-select-container">
                      <el-select v-model="formInline.designateDeptCode" placeholder="请选择服务部门" @change="deptChange">
                        <el-option v-for="item in teamsOptions" :key="item.id" :label="item.team_name" :value="item.id + '_' + item.team_name"> </el-option>
                      </el-select>
                      <el-button type="primary" size="mini" @click="openDeptSelectDialog('serviceDept')">选择</el-button>
                    </div>
                  </el-form-item>
                  <el-form-item style="flex: 1" label="" prop="designCheck" class="person-form-item">
                    <el-checkbox v-model="formInline.designCheck" @change="designPerson">指派工人</el-checkbox>
                  </el-form-item>
                </div>
                <table v-if="selectTeamPeopleRow.length" class="maint-table" style="table-layout: fixed">
                  <tbody>
                    <tr>
                      <td style="width: 42%">服务人员</td>
                      <td style="width: 42%">人员电话</td>
                      <td style="width: 16%">操作</td>
                    </tr>
                    <tr v-for="(item, index) in selectTeamPeopleRow" :key="index">
                      <td>
                        <div :title="item.member_name" class="one-line">{{ item.member_name }}</div>
                      </td>
                      <td>
                        <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                      </td>
                      <td>
                        <div class="one-line scope-del" @click="peopleDel(item.id)">删除</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div class="formRow repair-work">
                  <el-form-item label="" prop="repairWork">
                    <el-checkbox v-model="formInline.repairWork" true-label="2" false-label="1">返修工单</el-checkbox>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </ContentCard>
        </el-col>
      </el-row>
    </div>
    <!-- 选择班组人员弹框 -->
    <template v-if="changeNoticePeopleShow">
      <noticePeople ref="changeNoticePeople" :changeNoticePeopleShow="changeNoticePeopleShow" @peopleSure="noticePeopleSure" @closeDialog="closeNoticePeopleDialog"></noticePeople>
    </template>
    <!-- 选择班组人员弹框 -->
    <template v-if="changeTeamsPeopleShow">
      <teamsPeople
        ref="changeTeamsPeople"
        :selectTeamsData="selectTeamsData"
        :changeTeamsPeopleShow="changeTeamsPeopleShow"
        @peopleSure="peopleSure"
        @closeDialog="closePeopleDialog"
      ></teamsPeople>
    </template>
    <!-- 选择服务地点弹框 -->
    <template v-if="changeLocationShow">
      <Location ref="changeLocation" :changeLocationShow="changeLocationShow" @localSure="locationSure" @closeDialog="closeLocationDialog"></Location>
    </template>
    <!-- 创建工单 -->
    <template v-if="workOrderDealShow">
      <CreatedWorkOrder
        :workOrderDealShow.sync="workOrderDealShow"
        :workTypeCode="olgTaskManagement.workTypeCode"
        :workTypeName="olgTaskManagement.workTypeName"
        :alarmId="alarmId"
        :spaceId="spaceId"
        :projectCode="projectCode"
        :dealType="dealType"
        :workTypeId="workTypeId"
      ></CreatedWorkOrder>
    </template>
    <!-- 科室选择弹框 -->
    <el-dialog
      title="选择科室"
      :visible.sync="sourceDeptSelectDialogVisible"
      width="50%"
      :before-close="closeSourceDeptSelectDialog"
      :close-on-click-modal="false"
      custom-class="dept-select-dialog"
      append-to-body
    >
      <div class="dept-container">
        <!-- 查询条件 -->
        <div class="search-container">
          <el-input v-model="searchSourceDeptName" placeholder="请输入科室名称" prefix-icon="el-icon-search" clearable @input="filterSourceDepts"></el-input>
        </div>
        <div class="dept-list-container">
          <el-radio-group v-model="selectedSourceDeptValue" class="dept-radio-group">
            <el-radio v-for="item in filteredSourceDeptOptions" :key="item.value" :label="item.value" class="dept-radio-item">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeSourceDeptSelectDialog">取 消</el-button>
        <el-button type="primary" @click="confirmSourceDeptSelect">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 服务部门选择弹框 -->
    <el-dialog
      title="选择服务部门"
      :visible.sync="deptSelectDialogVisible"
      width="50%"
      :before-close="closeDeptSelectDialog"
      :close-on-click-modal="false"
      custom-class="dept-select-dialog"
      append-to-body
    >
      <div class="dept-table-container">
        <!-- 查询条件 -->
        <div class="search-container">
          <el-form :inline="true" class="search-form">
            <el-form-item label="请选择公司">
              <el-select v-model="searchCompanyId" placeholder="请选择公司" clearable>
                <el-option v-for="item in companyOptions" :key="item.id" :label="item.companyName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="请输入班组名称">
              <el-input v-model="searchTeamName" placeholder="请输入班组名称" clearable @keyup.enter.native="handleSearchChange"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearchChange">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table
          ref="deptTable"
          :data="deptTableData"
          border
          height="300"
          highlight-current-row
          style="width: 100%"
          :select-on-indeterminate="false"
          @row-click="handleDeptRowClick"
          @selection-change="handleDeptSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" :selectable="handleSelectable"></el-table-column>
          <el-table-column type="index" :index="getTableIndex" label="序号" width="100" align="center"></el-table-column>
          <el-table-column prop="teamName" label="班组名称" width="150" show-overflow-tooltip></el-table-column>
          <el-table-column prop="companyName" label="所属公司" width="150" show-overflow-tooltip></el-table-column>
          <el-table-column prop="describe" label="描述" show-overflow-tooltip></el-table-column>
          <el-table-column prop="num" label="人员数量" width="100" align="center"></el-table-column>
          <el-table-column prop="phone" label="联系电话" width="150" show-overflow-tooltip></el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination
          :current-page="pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          class="dept-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDeptSelectDialog">取 消</el-button>
        <el-button type="primary" @click="confirmDeptSelect">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import noticePeople from '../common/noticePeople.vue'
import teamsPeople from '../common/teamsPeople.vue'
import Location from '../common/Location.vue'
import moment from 'moment'
import { listToTree } from '@/util'
export default {
  name: 'olgMaintenance',
  components: {
    noticePeople,
    teamsPeople,
    Location
  },
  props: {
    workOrderDetail: {
      type: Object,
      default() {
        return {}
      }
    },
    alarmId: {
      type: String,
      default: ''
    },
    spaceId: {
      type: String,
      default: ''
    },
    projectCode: {
      type: String,
      default: ''
    },
    dealType: {
      type: String,
      default: ''
    },
    routerFrom: {
      type: String,
      default: 'local'
    }
  },
  data() {
    return {
      itemTreeData: [],
      defaultProps: {
        label: 'name',
        children: 'children'
      },
      filterText: '',
      selectService: '',
      rules: {},
      shouldValidateTypeSources: false,
      formInline: {
        needPhone: '',
        appointmentType: '0',
        appointmentDate: '',
        urgencyDegree: '2',
        sourcesDept: '',
        localtion: '',
        localtionName: '',
        noticeBtn: false,
        designCheck: false,
        typeSources: '',
        callerJobNum: '',
        callerName: '',
        sourcesPhone: '',
        designateDeptCode: '',
        questionDescription: '',
        repairWork: '',
        designatePersonCode: '',
        designatePersonName: '',
        designatePersonPhone: '',
        personHidden: '',
        typeThree: '',
        typeNameThree: '',
        typeTwo: '',
        typeNameTwo: '',
        typeOne: '',
        typeNameOne: '',
        isDispatching: '0',
        requireAccomplishDate: ''
      },
      kd_time: '',
      selectRowId: '', // tree 选中id
      teamsOptions: [], // 班组
      sourcesDeptOptions: [],
      urgencyOptions: [], // 紧急程度字典
      typeSourcesOptions: [], // 申报属性字典
      requiredFields: [], // 接口返回的必填字段
      changeNoticePeopleShow: false, // 选择 应急联系人弹框
      selectNoticePeopleRow: [],
      changeTeamsPeopleShow: false,
      selectTeamPeopleRow: [],
      selectTeamsData: {},
      changeLocationShow: false, // 服务地点弹窗
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      workOrderDealShow: false,
      olgTaskManagement: {},
      workTypeId: '',
      deptSelectDialogVisible: false, // 服务部门选择弹框显示状态
      deptTableData: [], // 服务部门表格数据
      pageSize: 10, // 每页显示条数
      pageNo: 1, // 当前页码
      totalCount: 0, // 总数据量
      selectedDept: null, // 选中的服务部门
      searchCompanyId: '', // 查询条件-公司ID
      searchTeamName: '', // 查询条件-班组名称
      companyOptions: [], // 公司下拉选项
      itemTop5List: [], // 服务事项TOP5列表
      selectedTop5Index: -1, // 当前选中的TOP5标签索引
      locationOptions: [], // 服务地点下拉选项
      // 科室选择相关
      sourceDeptSelectDialogVisible: false, // 科室选择弹框显示状态
      selectedSourceDeptValue: '', // 选中的科室值
      searchSourceDeptName: '', // 查询条件-科室名称
      filteredSourceDeptOptions: [], // 过滤后的科室选项
      allSourceDeptOptions: [], // 所有科室选项(保存从接口获取的完整科室列表)
      currentSelectType: '', // 当前打开的选择类型（科室/服务部门）
      olgWorkPushNew: {} // 获取配置参数
    }
  },
  computed: {
    // 判断是否为巡检类工单（巡检报修工单或巡检自修工单）
    isInspectionWorkOrder() {
      const workTypeCode = this.workOrderDetail?.olgTaskManagement?.workTypeCode
      return workTypeCode === '10' || workTypeCode === '8' || workTypeCode === 10 || workTypeCode === 8
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
      // 搜索后展开匹配的节点
      if (val) {
        this.$nextTick(() => {
          this.expandMatchedNodes(val)
        })
      }
    },
    'formInline.typeSources': {
      handler(val) {
        if (val && this.shouldValidateTypeSources) {
          // 当申报属性值变更后，重新校验字段
          this.$nextTick(() => {
            this.$refs.formInline && this.$refs.formInline.validateField('typeSources')
          })
        }
        this.shouldValidateTypeSources = true // 第一次加载后激活校验
      }
    }
  },
  created() {
    if (this.workOrderDetail) {
      this.formInit()
    }
  },
  mounted() {
    this.getNewSysConfigParam()
    this.getPersonnelDictionary('1')
    this.getPersonnelDictionary('2')
    this.getItemTop5()
    this.getItemTreeData()
    this.getIsRequired()
    this.getSourcesDeptOptions() // 获取科室数据
    // 初始化科室选择器数据
    this.$nextTick(() => {
      this.updateFilteredSourceDeptOptions()
    })
    // 在表单内容初始化后清空校验状态
    this.$nextTick(() => {
      this.$refs.formInline && this.$refs.formInline.clearValidate()
    })
    // 处理模式 初始化查询班组  新增模式由服务事项带出班组
    if (this.dealType === 'add') {
      console.log(this.$store.state.user.userInfo)
      // 带入新增该人员的信息
      // this.formInline.callerName = this.$store.state.user.userInfo.user.staffName
      this.formInline.callerJobNum = this.$store.state.user.userInfo.user.staffNum
      // this.formInline.sourcesPhone = this.$store.state.user.userInfo.user.userName
      if (this.spaceId) {
        this.getAreaDataByAreaCode(this.spaceId) // 根据区域编码获取区域数据
      }
    }
  },
  methods: {
    // 获取文件名并截取前8个字符
    getFileName(url) {
      if (!url) return '未知文件'
      // 从URL中提取文件名
      const fileName = url.substring(url.lastIndexOf('/') + 1)
      if (!fileName) return '录音文件'

      // 截取前8个字符，超出部分用省略号表示
      if (fileName.length > 10) {
        return fileName.substring(0, 10) + '...'
      }
      return fileName
    },
    // 播放音频文件
    playAudio(audioUrl) {
      if (!audioUrl) return
      // 创建临时音频元素
      const audio = new Audio(this.$tools.imgUrlTranslation(audioUrl))
      audio.play().catch((error) => {
        console.error('播放音频失败:', error)
        this.$message.error('播放音频失败，请稍后再试')
      })
    },
    // 下载音频文件
    downloadAudio(audioUrl) {
      if (!audioUrl) return

      // 获取转换后的URL
      const processedUrl = this.$tools.imgUrlTranslation(audioUrl)
      console.log('下载音频URL:', processedUrl)

      // 使用fetch API获取文件内容并直接下载
      fetch(processedUrl)
        .then((response) => {
          if (!response.ok) {
            throw new Error('网络响应异常')
          }
          return response.blob()
        })
        .then((blob) => {
          // 创建一个Blob URL
          const blobUrl = window.URL.createObjectURL(blob)

          // 创建一个隐藏的a标签进行下载
          const link = document.createElement('a')
          link.href = blobUrl

          // 从URL中提取文件名
          const fileName = processedUrl.substring(processedUrl.lastIndexOf('/') + 1)
          link.download = fileName || '录音.mp3'

          // 添加到DOM，触发点击，然后移除
          document.body.appendChild(link)
          link.click()

          // 延迟移除元素和释放Blob URL
          setTimeout(() => {
            document.body.removeChild(link)
            window.URL.revokeObjectURL(blobUrl)
          }, 100)
        })
        .catch((error) => {
          console.error('下载音频失败:', error)
          this.$message.error('下载音频失败，请稍后再试')
        })
    },
    getNewSysConfigParam() {
      this.$api.getNewSysConfigParam().then((res) => {
        if (res.code === '200' && res.data) {
          this.olgWorkPushNew = res.data.olgWorkPushNew
        }
      })
    },
    getItemTop5() {
      this.$api
        .getItemTop5({ workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode })
        .then((res) => {
          if (res.success && res.body && res.body.itemTop5) {
            this.itemTop5List = res.body.itemTop5
          }
        })
        .catch((err) => {
          console.error('获取服务事项TOP5失败', err)
        })
    },
    // 选择TOP5服务事项
    selectTop5Item(item, index) {
      // 设置当前选中的标签索引
      this.selectedTop5Index = index
      // 直接设置服务事项值 - 兼容接口返回的字段错误
      // 实际上: itemServiceCode/itemServiceName 是第三级, itemDetailCode/itemDetailName 是第二级
      this.selectService = item.itemServiceName // 第三级名称显示
      this.selectRowId = item.itemTypeCode
      // 设置表单值 - 按照实际层级正确映射
      this.formInline.typeThree = item.itemServiceCode // 第三级Code
      this.formInline.typeNameThree = item.itemServiceName // 第三级Name
      this.formInline.typeTwo = item.itemDetailCode // 第二级Code
      this.formInline.typeNameTwo = item.itemDetailName // 第二级Name
      this.formInline.typeOne = item.itemTypeCode // 第一级Code
      this.formInline.typeNameOne = item.itemTypeName // 第一级Name
      // 触发服务事项的表单校验
      this.$nextTick(() => {
        this.$refs.formInline && this.$refs.formInline.validateField('typeThree')
      })
      // 重置服务部门选择
      this.formInline.designateDeptCode = ''
      this.selectTeamsData = {}
      this.formInline.isDispatching = '0'
      this.selectTeamPeopleRow = []
      this.formInline.designCheck = false
      // 获取对应的班组，并自动选中第一个
      this.getTeamsByWorkTypeCode()
    },
    getIsRequired() {
      this.$api.getIsRequired({ workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode }).then((res) => {
        if (res.code === '200' && res.data) {
          // 将必填字段拆分为数组
          this.requiredFields = res.data.requiredCode ? res.data.requiredCode.split(',') : []
          // 动态设置表单验证规则
          this.setupFormValidation(this.requiredFields)
          // 设置完验证规则后清空校验状态
          this.$nextTick(() => {
            this.$refs.formInline && this.$refs.formInline.clearValidate()
          })
        }
      })
    },
    setupFormValidation(requiredFields) {
      // 初始化规则对象
      this.rules = {}
      // 申报属性强制设为必填项
      this.rules.typeSources = [{ required: true, message: '请选择申报属性', trigger: 'blur' }]
      // 字段和对应的中文名称映射
      const fieldLabels = {
        sourcesDept: '所属科室',
        localtion: '服务地点',
        localtionName: '服务地点',
        itemTypeCode: '服务事项',
        typeThree: '服务事项', // 实际表单使用typeThree
        typeSources: '申报属性',
        callerJobNum: '工号',
        callerName: '联系人',
        sourcesPhone: '电话',
        questionDescription: '申报描述',
        designateDeptCode: '服务部门'
      }
      // 遍历必填字段，为每个字段设置验证规则
      requiredFields.forEach((field) => {
        // 如果是巡检类工单，所属科室和要求完工时间不需要校验
        if (this.isInspectionWorkOrder && (field === 'requireAccomplishDate')) {
          return
        }
        // 特殊处理服务地点字段
        if (field === 'localtion') {
          field = 'localtionName' // 表单中实际使用的是localtionName
        } else if (field === 'itemTypeCode') {
          field = 'typeThree' // 服务事项在表单中使用typeThree
        }
        // 针对不同字段使用不同的提示语
        if (fieldLabels[field]) {
          // 下拉框类型的字段使用"请选择"提示
          const selectFields = ['designateDeptCode', 'sourcesDept', 'typeSources']
          const message = selectFields.includes(field) ? `请选择${fieldLabels[field]}` : `请输入${fieldLabels[field]}`
          this.rules[field] = [{ required: true, message, trigger: 'change' }]
        }
      })
    },
    getPersonnelDictionary(type) {
      this.$api.getPersonnelDictionary({ type: type }).then((res) => {
        if (res.code == '200') {
          if (type == '1') {
            this.urgencyOptions = res.data
          } else if (type == '2') {
            this.typeSourcesOptions = res.data
          }
        }
      })
    },
    // 判断字段是否必填
    isFieldRequired(field) {
      // 申报属性特殊处理，始终返回true
      if (field === 'typeSources') {
        return true
      }
      // 检查字段是否在必填字段数组中
      return this.requiredFields.includes(field)
    },
    // 表单提交
    saveForm(options = {}) {
      const formData = JSON.parse(JSON.stringify(this.formInline))
      // 添加暂存参数
      if (options.isTemporary) {
        formData.isTemporary = options.isTemporary
      }
      // 根据接口返回的必填字段动态判断服务事项是否必填
      if (this.isFieldRequired('itemTypeCode') && !this.selectService) {
        return this.$message.warning('请选择三级服务事项！')
      }
      // 校验预约时间
      if (formData.appointmentType === '1' && !formData.appointmentDate) {
        return this.$message.warning('请选择预约时间！')
      }
      // 校验申报属性字段
      if (formData.typeSources === '') {
        return this.$message.warning('请选择申报属性')
      }
      const personWorkPerson = []
      if (formData.designatePersonCode) {
        const designatePersonCode = formData.designatePersonCode.split(',')
        const designatePersonName = formData.designatePersonName.split(',')
        const designatePersonPhone = formData.designatePersonPhone.split(',')
        designatePersonCode.map((e, index) => {
          personWorkPerson.push([designatePersonName[index], designatePersonPhone[index], '', e].toString())
        })
      }
      // 基础参数
      const baseParams = {
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        officeName: '',
        needPhone1: formData.needPhone,
        typeThree1: formData.typeThree,
        typeNameThree1: formData.typeNameThree,
        typeOne1: formData.typeOne,
        typeNameOne1: formData.typeNameOne,
        typeTwo1: formData.typeTwo,
        typeNameTwo1: formData.typeNameTwo,
        appointmentType1: formData.appointmentType,
        appointmentDate1: formData.appointmentDate,
        urgencyDegree1: formData.urgencyDegree,
        localtionName1: formData.localtionName,
        designateDeptCode1: formData.designateDeptCode,
        typeSources1: formData.typeSources,
        repairWork1: formData.repairWork,
        num: 1
      }
      // 如果不是巡检类工单，添加要求完工时间和所属科室参数
      if (!this.isInspectionWorkOrder) {
        baseParams.requireAccomplishDate = formData.requireAccomplishDate
      }
      Object.assign(formData, baseParams)
      if (this.dealType === 'deal') {
        const dealParams = {
          operSource: 'newCreate',
          operType: this.workOrderDetail.operType,
          workNum: this.workOrderDetail.olgTaskManagement.workNum,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
          type: this.workOrderDetail.olgTaskManagement.type,
          id: this.workOrderDetail.olgTaskManagement.id,
          personWorkPerson: personWorkPerson.length ? personWorkPerson.toString() : ''
        }
        // 如果不是巡检类工单，添加所属科室参数
        if (formData.sourcesDept) {
          dealParams.sourceDept = formData.sourcesDept.split('_')[1]
          dealParams.sourcesDept1 = formData.sourcesDept
        }
        Object.assign(formData, dealParams)
        this.placeAndCancelOrder(formData)
      } else if (this.dealType === 'dealToAdd') {
        // 暂存工单：回显用deal，提交用add
        const addParams = {
          id: this.workOrderDetail.olgTaskManagement.id,
          sysForShort: this.alarmId,
          workNum: this.workOrderDetail.olgTaskManagement.workNum,
          workSources: this.workOrderDetail.olgTaskManagement.workSources,
          operType: this.workOrderDetail.operType,
          taskType: this.workOrderDetail.olgTaskManagement.taskType,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
          workTypeName: this.workOrderDetail.olgTaskManagement.workTypeName,
          type: this.workOrderDetail.olgTaskManagement.type,
          tempDate: this.workOrderDetail.tempDate,
          isSubmit: '0',
          noNum: this.$store.state.user.userInfo.user.staffNumber,
          personWorkPerson: personWorkPerson.length ? personWorkPerson.toString() : ''
        }
        // 处理科室信息
        // if (!this.isInspectionWorkOrder) {
          if (formData.sourcesDept) {
            addParams.sourceDept = formData.sourcesDept.split('_')[1]
            addParams.sourcesDept1 = formData.sourcesDept
            addParams.sourcesDeptName = formData.sourcesDept.split('_')[2] || ''
          } else if (this.workOrderDetail.olgTaskManagement.sourcesDept) {
            addParams.sourceDept = this.workOrderDetail.olgTaskManagement.sourcesDept
            addParams.sourcesDept1 = this.workOrderDetail.olgTaskManagement.sourcesDept + '_' + this.workOrderDetail.olgTaskManagement.sourcesDeptName
            addParams.sourcesDeptName = this.workOrderDetail.olgTaskManagement.sourcesDeptName === 'undefined' ? '' : this.workOrderDetail.olgTaskManagement.sourcesDeptName
          }
        // }
        Object.assign(formData, addParams)
        this.placeAndCancelSave(formData)
      } else if (this.dealType === 'update') {
        // 修改工单操作
        const updateParams = {
          operSource: 'newCreate',
          operType: 'updateOrder', // 指定operType为updateOrder
          workNum: this.workOrderDetail.olgTaskManagement.workNum,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
          type: this.workOrderDetail.olgTaskManagement.type,
          id: this.workOrderDetail.olgTaskManagement.id,
          personWorkPerson: personWorkPerson.length ? personWorkPerson.toString() : ''
        }
        // 如果不是巡检类工单，添加所属科室参数
        // if (!this.isInspectionWorkOrder && formData.sourcesDept) {
          updateParams.sourceDept = formData.sourcesDept.split('_')[1]
          updateParams.sourcesDept1 = formData.sourcesDept
        // }
        Object.assign(formData, updateParams)
        this.placeAndCancelOrder(formData)
      } else if (this.dealType === 'add') {
        const addParams = {
          id: '',
          sysForShort: this.alarmId,
          workNum: this.workOrderDetail.olgTaskManagement.workNum,
          workSources: this.workOrderDetail.olgTaskManagement.workSources,
          operType: this.workOrderDetail.operType,
          taskType: this.workOrderDetail.olgTaskManagement.taskType,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
          workTypeName: this.workOrderDetail.olgTaskManagement.workTypeName,
          type: this.workOrderDetail.olgTaskManagement.type,
          tempDate: this.workOrderDetail.tempDate,
          isSubmit: '0',
          noNum: this.$store.state.user.userInfo.user.staffNumber,
          personWorkPerson: personWorkPerson.length ? personWorkPerson.toString() : ''
        }
        // 如果不是巡检类工单，添加所属科室参数
        // if (!this.isInspectionWorkOrder) {
          if (formData.sourcesDept) {
            addParams.sourceDept = formData.sourcesDept.split('_')[1]
            addParams.sourcesDept1 = formData.sourcesDept
          } else {
            // 如果formData.sourcesDept为空，则使用workOrderDetail中的值
            addParams.sourcesDept1 = this.workOrderDetail.olgTaskManagement.sourcesDept + '_' + this.workOrderDetail.olgTaskManagement.sourcesDeptName
            addParams.sourcesDeptName = this.workOrderDetail.olgTaskManagement.sourcesDeptName === 'undefined' ? '' : this.workOrderDetail.olgTaskManagement.sourcesDeptName
          }
        // }
        Object.assign(formData, addParams)
        this.placeAndCancelSave(formData)
      }
    },
    placeAndCancelOrder(formData) {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          this.$api.placeAndCancelOrder(formData).then((res) => {
            if (res.success) {
              this.$message({
                message: res.msg,
                type: 'success'
              })
              this.$emit('save', false)
            } else {
              this.$message({
                message: res.msg,
                type: 'warning'
              })
            }
          })
        } else {
          // 校验申报属性字段
          if (this.formInline.typeSources === '') {
            this.$message.warning('请选择申报属性')
          }
          console.log('error submit!!')
          return false
        }
      })
    },
    placeAndCancelSave(formData) {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          this.$api.placeAndCancelSave(formData).then((res) => {
            let msg = res.msg
            msg = msg.replace('<br/>', '')
            if (res.success) {
              this.$message({
                message: msg,
                type: 'success'
              })
              this.$emit('save')
              // this.$emit('save', {
              //   workNum: res.body.workNum,
              //   workType: this.workOrderDetail.olgTaskManagement.workTypeCode,
              //   workTypeName: this.workOrderDetail.olgTaskManagement.workTypeName,
              //   limAlarmId: this.alarmId
              // })
            } else {
              this.$message({
                message: msg,
                type: 'warning'
              })
            }
          })
        } else {
          // 校验申报属性字段
          if (this.formInline.typeSources === '') {
            this.$message.warning('请选择申报属性')
          }
          console.log('error submit!!')
          return false
        }
      })
    },
    formInit() {
      if (this.dealType === 'deal' || this.dealType === 'update' || this.dealType === 'dealToAdd') {
        console.log('this.workOrderDetail', this.workOrderDetail)
        this.formInline.needPhone = this.workOrderDetail.olgTaskManagement.needPhone
        this.formInline.appointmentType = this.workOrderDetail.olgTaskManagement.appointmentType || '0'
        this.formInline.appointmentDate = this.workOrderDetail.olgTaskManagement.appointmentDate
          ? moment(Number(this.workOrderDetail.olgTaskManagement.appointmentDate)).format('YYYY-MM-DD HH:mm:ss')
          : ''
        this.formInline.requireAccomplishDate = this.workOrderDetail.olgTaskManagement.requireAccomplishDate
        this.formInline.urgencyDegree = this.workOrderDetail.olgTaskManagement.urgencyDegree || '2'
        // 安全访问 olgTaskDetailList，防止接口未返回该字段时报错
        const taskDetailList = this.workOrderDetail.olgTaskManagement.olgTaskDetailList
        if (taskDetailList && taskDetailList.length > 0) {
          this.formInline.localtion = taskDetailList[0].localtion
          this.formInline.localtionName = taskDetailList[0].localtionName
          // 初始化服务地点选项
          if (this.formInline.localtion && this.formInline.localtionName) {
            this.locationOptions.push({
              id: this.formInline.localtion,
              name: this.formInline.localtionName
            })
          }
        }
        // 初始化所属科室
        if (this.workOrderDetail.olgTaskManagement.sourcesDept && this.workOrderDetail.olgTaskManagement.sourcesDeptName) {
          // 添加到科室选项中
          this.sourcesDeptOptions.push({
            value: this.workOrderDetail.olgTaskManagement.sourcesDept,
            label: this.workOrderDetail.olgTaskManagement.sourcesDeptName
          })
          // 设置选中值
          this.formInline.sourcesDept = this.workOrderDetail.olgTaskManagement.sourcesDept + '_' + this.workOrderDetail.olgTaskManagement.sourcesDeptName
        }
        this.formInline.callerJobNum = this.workOrderDetail.olgTaskManagement.callerJobNum
        this.formInline.callerName = this.workOrderDetail.olgTaskManagement.callerName
        this.formInline.sourcesPhone = this.workOrderDetail.olgTaskManagement.sourcesPhone
        this.formInline.questionDescription = this.workOrderDetail.olgTaskManagement.questionDescription
        this.formInline.typeSources = this.workOrderDetail.olgTaskManagement.typeSources || '1'
        this.formInline.repairWork = this.workOrderDetail.olgTaskManagement.repairWork

        // 初始化服务部门回显
        if (this.workOrderDetail.olgTaskManagement.designateDeptCode && this.workOrderDetail.olgTaskManagement.designateDeptName) {
          // 添加到服务部门选项中
          const deptExists = this.teamsOptions.some((item) => item.id === this.workOrderDetail.olgTaskManagement.designateDeptCode)
          if (!deptExists) {
            this.teamsOptions.push({
              id: this.workOrderDetail.olgTaskManagement.designateDeptCode,
              team_name: this.workOrderDetail.olgTaskManagement.designateDeptName
            })
          }
          // 设置选中值
          this.formInline.designateDeptCode = this.workOrderDetail.olgTaskManagement.designateDeptCode + '_' + this.workOrderDetail.olgTaskManagement.designateDeptName
        }

        // 初始化指派工人回显
        if (this.workOrderDetail.olgTaskManagement.designatePersonCode) {
          this.formInline.designatePersonCode = this.workOrderDetail.olgTaskManagement.designatePersonCode
          this.formInline.designatePersonName = this.workOrderDetail.olgTaskManagement.designatePersonName || ''
          this.formInline.designatePersonPhone = this.workOrderDetail.olgTaskManagement.designatePersonPhone || ''

          // 构建指派工人显示数据
          const personCodes = this.workOrderDetail.olgTaskManagement.designatePersonCode.split(',')
          const personNames = (this.workOrderDetail.olgTaskManagement.designatePersonName || '').split(',')
          const personPhones = (this.workOrderDetail.olgTaskManagement.designatePersonPhone || '').split(',')

          this.selectTeamPeopleRow = personCodes.map((code, index) => ({
            id: code,
            member_name: personNames[index] || '',
            phone: personPhones[index] || ''
          }))

          // 设置指派工人复选框为选中状态
          this.formInline.designCheck = true
        }

        // 安全访问 olgTaskDetailList，防止接口未返回该字段时报错
        if (taskDetailList && taskDetailList.length > 0) {
          this.selectService = taskDetailList[0].itemServiceName || ''
          this.formInline.typeThree = taskDetailList[0].itemServiceCode
          this.formInline.typeNameThree = taskDetailList[0].itemServiceName
          this.formInline.typeOne = taskDetailList[0].itemTypeCode
          this.formInline.typeNameOne = taskDetailList[0].itemTypeName
          this.formInline.typeTwo = taskDetailList[0].itemDetailCode
          this.formInline.typeNameTwo = taskDetailList[0].itemDetailName
        } else {
          // 如果没有 olgTaskDetailList 数据，设置默认值
          this.selectService = ''
          this.formInline.typeThree = ''
          this.formInline.typeNameThree = ''
          this.formInline.typeOne = ''
          this.formInline.typeNameOne = ''
          this.formInline.typeTwo = ''
          this.formInline.typeNameTwo = ''
        }
        this.kd_time = this.workOrderDetail.kd_time
        // 过滤班组
        this.selectRowId = this.formInline.typeOne
        this.getTeamsByWorkTypeCode('Echo')
        // const spaceIds = this.formInline.localtion ? this.formInline.localtion.split(',') : []
        // if (spaceIds.length) {
        //   this.getAreaDataByAreaCode(spaceIds[spaceIds.length - 1])
        // }
      } else if (this.dealType === 'add') {
        this.kd_time = moment().format('YYYY-MM-DD HH:mm:ss')
      }
    },
    // 服务事项赋值
    handleNodeClick(data, node) {
      if (data.level !== '3') {
        return this.$message.warning('请选择三级服务事项！')
      }
      if (data.level === '3') {
        this.selectService = data.name
        this.selectRowId = node.parent.parent.data.id
        this.formInline.typeThree = data.id
        this.formInline.typeNameThree = data.name
        this.formInline.typeTwo = node.parent.data.id
        this.formInline.typeNameTwo = node.parent.data.name
        this.formInline.typeOne = node.parent.parent.data.id
        this.formInline.typeNameOne = node.parent.parent.data.name
        // 触发服务事项的表单校验
        this.$nextTick(() => {
          this.$refs.formInline.validateField('typeThree')
        })
      } else if (data.level === '2') {
        this.selectRowId = node.parent.data.id
        this.formInline.typeTwo = data.id
        this.formInline.typeNameTwo = data.name
      } else {
        this.selectRowId = node.data.id
        this.formInline.typeOne = data.id
        this.formInline.typeNameOne = data.name
      }
      // 重置服务部门相关数据
      this.formInline.designateDeptCode = ''
      this.selectTeamsData = {}
      this.formInline.isDispatching = '0'
      this.selectTeamPeopleRow = []
      this.formInline.designCheck = false
      // 获取对应的班组，并自动选中第一个
      this.getTeamsByWorkTypeCode()
    },
    filterNode(value, data, node) {
      if (!value) return true

      // 检查当前节点是否匹配搜索关键词
      const currentNodeMatches = data.name.indexOf(value) !== -1

      // 如果当前节点匹配，则显示该节点（其所有子级会自动显示）
      if (currentNodeMatches) {
        return true
      }

      // 检查是否有祖先节点匹配搜索关键词
      // 如果有祖先节点匹配，则当前节点也应该显示
      if (this.hasMatchingAncestor(node, value)) {
        return true
      }

      // 如果当前节点不匹配，检查是否有子级匹配
      // 这样可以确保父级节点在子级匹配时也显示
      if (data.children && data.children.length > 0) {
        return this.hasMatchingChildren(data.children, value)
      }

      return false
    },

    // 递归检查子级是否有匹配的节点
    hasMatchingChildren(children, value) {
      for (let child of children) {
        // 如果子级节点匹配
        if (child.name.indexOf(value) !== -1) {
          return true
        }
        // 递归检查子级的子级
        if (child.children && child.children.length > 0) {
          if (this.hasMatchingChildren(child.children, value)) {
            return true
          }
        }
      }
      return false
    },

    // 检查祖先节点是否有匹配的
    hasMatchingAncestor(node, value) {
      if (!node || !node.parent) return false

      // 检查父节点是否匹配
      if (node.parent.data && node.parent.data.name && node.parent.data.name.indexOf(value) !== -1) {
        return true
      }

      // 递归检查更上级的祖先节点
      return this.hasMatchingAncestor(node.parent, value)
    },

    // 展开匹配的节点
    expandMatchedNodes(value) {
      if (!this.$refs.tree || !this.itemTreeData) return

      // 递归查找并展开匹配的节点
      const expandNode = (nodes) => {
        nodes.forEach((node) => {
          // 如果当前节点匹配搜索关键词，展开该节点
          if (node.name.indexOf(value) !== -1) {
            // 使用 Element UI 的正确方法展开节点
            const treeNode = this.$refs.tree.getNode(node.id)
            if (treeNode) {
              treeNode.expanded = true
            }
            // 如果有子级，也展开所有子级
            if (node.children && node.children.length > 0) {
              this.expandAllChildren(node.children)
            }
          } else if (node.children && node.children.length > 0) {
            // 如果当前节点不匹配，但有子级匹配，也要展开当前节点
            if (this.hasMatchingChildren(node.children, value)) {
              const treeNode = this.$refs.tree.getNode(node.id)
              if (treeNode) {
                treeNode.expanded = true
              }
            }
            // 递归处理子级
            expandNode(node.children)
          }
        })
      }

      expandNode(this.itemTreeData)
    },

    // 展开所有子级节点
    expandAllChildren(children) {
      children.forEach((child) => {
        const treeNode = this.$refs.tree.getNode(child.id)
        if (treeNode) {
          treeNode.expanded = true
        }
        if (child.children && child.children.length > 0) {
          this.expandAllChildren(child.children)
        }
      })
    },
    // 再建一单
    placeOrder() {
      this.olgTaskManagement = {
        workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
        workTypeName: this.workOrderDetail.olgTaskManagement.workTypeName
      }
      if (this.dealType === 'deal') {
        this.workTypeId = this.workOrderDetail.olgTaskManagement.id
      }
      this.workOrderDealShow = true
      // id workTypeName workTypeCode 'newCreate'
    },
    // 通知联系人
    noticeBtnChange() {
      this.changeNoticePeopleShow = true
    },
    urgencyChange() {
      this.selectNoticePeopleRow = []
      this.formInline.num = 0
      this.formInline.noticeBtn = ''
    },
    deptChange() {
      this.selectTeamPeopleRow = []
      this.setFormPeopleData([])
    },
    // 指派工人
    designPerson() {
      if (!this.formInline.designateDeptCode) {
        this.$nextTick(() => {
          this.formInline.designCheck = !this.formInline.designCheck
        })
        return this.$message({
          message: '请选择服务部门！',
          type: 'warning'
        })
      }
      this.selectTeamsData = {
        type: '2',
        filterData: true,
        id: this.formInline.designateDeptCode.split('_')[0],
        deptName: this.formInline.designateDeptCode.split('_')[1]
      }
      this.formInline.isDispatching = '1'
      this.changeTeamsPeopleShow = true
    },
    // 获取服务事项
    getItemTreeData() {
      const params = {
        workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
        free1: this.projectCode
        // free1: '73e7aab447b34971b9ae6d8dae034aa3'
      }
      this.$api.getItemTreeData(params).then((res) => {
        this.itemTreeData = listToTree(res, 'id', 'parent')
        if (this.dealType === 'add' && this.itemTreeData.length) {
          // 服务事项默认选中第一个
          // this.formInline.typeOne = this.itemTreeData[0].id
          // this.formInline.typeNameOne = this.itemTreeData[0].name
          // this.formInline.typeTwo = this.itemTreeData[0].children[0]?.id ?? ''
          // this.formInline.typeNameTwo = this.itemTreeData[0].children[0]?.name ?? ''
          // this.formInline.typeThree = this.itemTreeData[0].children[0]?.children[0]?.id ?? ''
          // this.formInline.typeNameThree = this.itemTreeData[0].children[0]?.children[0]?.name ?? ''
          // this.selectService = this.itemTreeData[0].children[0]?.children[0]?.name ?? ''
          // 过滤班组
          // this.selectRowId = this.itemTreeData[0].id
          this.getTeamsByWorkTypeCode('Echo')
        }
      })
    },
    getSourcesDeptOptions() {
      this.$api.getAllOffice().then((res) => {
        if (res.success) {
          // 保存所有科室数据用于选择弹框
          this.allSourceDeptOptions = res.body.result
          // 所属科室下拉框不显示数据，由服务地点关联带出
          this.sourcesDeptOptions = []
        }
      })
    },
    // 获取班组
    getTeamsByWorkTypeCode(flag) {
      const params = {
        workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
        matterId: this.selectRowId,
        itemTypeCode: this.selectRowId,
        itemServiceCode: this.formInline.typeTwo,
        itemDetailCode: this.formInline.typeThree
      }
      this.$api.getDataByTypeTeam(params).then((res) => {
        if (res.code === '200') {
          this.teamsOptions = res.data.list
          // 默认选中第一个班组
          // 当是回显(Echo)模式或者是通过服务事项选择触发的查询时，自动选中第一个服务部门
          if ((flag === 'Echo' || !flag) && this.teamsOptions.length && !this.formInline.designateDeptCode) {
            this.formInline.designateDeptCode = this.teamsOptions[0]?.id + '_' + this.teamsOptions[0]?.team_name
          }
        }
      })
    },
    // 获取服务地点 start
    getLocaltion() {
      this.changeLocationShow = true
    },
    locationSure(item) {
      // console.log(item)
      this.formInline.localtionName = item.name // 已经是下划线分隔的格式
      // 添加到地点选项中
      const exists = this.locationOptions.some((location) => location.id === item.id)
      if (!exists) {
        this.locationOptions.push({
          id: item.id,
          name: item.name
        })
      }
      // 设置选中值
      this.formInline.localtion = item.id
      this.changeLocationShow = false
      // 根据配置决定是否获取关联的科室
      // departmentServiceLocationCascade: 0-科室带出地点, 1-地点带出科室, 2-互相关联
      const cascadeType = this.olgWorkPushNew && this.olgWorkPushNew.departmentServiceLocationCascade
      if (cascadeType === '1' || cascadeType === '2') {
        // 调用接口获取关联的科室
        this.getDeptByLocalCode(item.id)
      }
    },
    // 根据服务地点获取关联的科室
    getDeptByLocalCode(localtionId) {
      this.$api
        .getDeptByLocalCode({ localtionId })
        .then((res) => {
          if (res.success && res.body && res.body.deptList && res.body.deptList.length > 0) {
            const deptList = res.body.deptList
            // 添加科室到下拉选项
            deptList.forEach((dept) => {
              // 检查是否已存在该科室，避免重复添加
              const isExist = this.sourcesDeptOptions.some((item) => item.value === dept.id)
              if (!isExist) {
                this.sourcesDeptOptions.push({
                  value: dept.id,
                  label: dept.name
                })
              }
            })
            // 默认选中第一个科室
            if (deptList.length > 0 && !this.formInline.sourcesDept) {
              const firstDept = deptList[0]
              this.formInline.sourcesDept = firstDept.id + '_' + firstDept.name
            }
          }
        })
        .catch((err) => {
          console.error('获取服务地点关联的科室失败', err)
        })
    },
    // 处理服务地点下拉框选择
    handleLocationChange(value) {
      const selectedLocation = this.locationOptions.find((item) => item.id === value)
      if (selectedLocation) {
        this.formInline.localtionName = selectedLocation.name
        // 根据配置决定是否获取关联的科室
        // departmentServiceLocationCascade: 0-科室带出地点, 1-地点带出科室, 2-互相关联
        const cascadeType = this.olgWorkPushNew && this.olgWorkPushNew.departmentServiceLocationCascade
        if (cascadeType === '1' || cascadeType === '2') {
          // 调用接口获取关联的科室
          this.getDeptByLocalCode(value)
        }
      }
    },
    // 处理来电号码失焦事件
    handleNeedPhoneBlur() {
      if (this.formInline.needPhone && this.formInline.needPhone.trim() !== '') {
        // 如果来电号码有值，同时赋值给电话
        this.formInline.sourcesPhone = this.formInline.needPhone
        this.getInformationByPhone(this.formInline.needPhone)
      }
    },
    // 处理电话失焦事件
    handlePhoneBlur() {
      if (this.formInline.sourcesPhone && this.formInline.sourcesPhone.trim() !== '') {
        this.getInformationByPhone(this.formInline.sourcesPhone)
      }
    },
    // 根据电话获取信息
    getInformationByPhone(phone) {
      this.$api
        .getInformationByPhone({ sourcesPhone: phone })
        .then((res) => {
          if (res.success && res.body && res.body.data) {
            const data = res.body.data
            // 处理科室信息
            if (data.sourcesDept && data.sourcesDeptName) {
              const deptValue = data.sourcesDept
              const deptName = data.sourcesDeptName
              // 检查是否已存在该科室，避免重复添加
              const isExist = this.sourcesDeptOptions.some((item) => item.value === deptValue)
              if (!isExist) {
                this.sourcesDeptOptions.push({
                  value: deptValue,
                  label: deptName
                })
              }
              // 设置科室值
              this.formInline.sourcesDept = deptValue + '_' + deptName
            }
            // 处理服务地点信息
            if (data.localtionCodes && data.localtionNames) {
              const locationCodes = data.localtionCodes.split(',')
              const locationNames = data.localtionNames.split(',')
              // 只处理第一个地点（如果有多个）
              if (locationCodes.length > 0 && locationNames.length > 0) {
                const locationId = locationCodes[0]
                const locationName = locationNames[0]
                // 检查是否已存在该地点，避免重复添加
                const exists = this.locationOptions.some((location) => location.id === locationId)
                if (!exists) {
                  this.locationOptions.push({
                    id: locationId,
                    name: locationName
                  })
                }
                // 设置服务地点值
                this.formInline.localtion = locationId
                this.formInline.localtionName = locationName
              }
            }
          }
        })
        .catch((err) => {
          console.error('获取电话关联信息失败', err)
        })
    },
    getAreaDataByAreaCode(spaceId) {
      const params = {
        id: spaceId
        // id: '1574997404942348290'
      }
      this.$api.lookUpDataById(params).then((res) => {
        // this.$api.lookUpDataById(params, __PATH.USER_CODE).then((res) => {
        if (res.code === 200) {
          const data = res.data
          // 使用下划线替换原来的>符号，并确保各级名称用下划线连接
          const simNameParts = data.simName.replace(/>/g, '_').split('_').filter(Boolean) // 过滤空字符串
          const locationName = simNameParts.join('_') + (data.ssmName ? '_' + data.ssmName : '')
          const simCode = data.simCode.split(',')
          const locationId = simCode[2] + '_' + simCode[3] + '_' + simCode[4] + '_' + data.id
          // 设置服务地点
          this.formInline.localtionName = locationName
          this.formInline.localtion = locationId
          // 添加到地点选项中
          const exists = this.locationOptions.some((location) => location.id === locationId)
          if (!exists) {
            this.locationOptions.push({
              id: locationId,
              name: locationName
            })
          }
          // 根据配置决定是否获取关联的科室
          const cascadeType = this.olgWorkPushNew && this.olgWorkPushNew.departmentServiceLocationCascade
          if (cascadeType === '1' || cascadeType === '2') {
            // 调用接口获取关联的科室
            this.getDeptByLocalCode(locationId)
          } else {
            // 直接使用返回数据中的科室信息
            // 科室数据加载完后再回显
            const timer = setInterval(() => {
              if (this.sourcesDeptOptions.length > 0) {
                this.formInline.sourcesDept = data.dmId ? data.dmId + '_' + data.dmName : ''
                clearInterval(timer)
              }
            }, 200)
          }
        }
      })
    },
    // end
    closeLocationDialog() {
      this.changeLocationShow = false
    },
    // 删除 应急联系人 start
    noticePeopleDel(id) {
      this.selectNoticePeopleRow = this.selectNoticePeopleRow.filter((e) => e.id !== id)
      this.setNoticePeople(this.selectNoticePeopleRow)
    },
    closeNoticePeopleDialog() {
      this.changeNoticePeopleShow = false
      this.setNoticePeople(this.changeNoticePeopleShow)
    },
    noticePeopleSure(item) {
      this.changeNoticePeopleShow = false
      this.selectNoticePeopleRow = item
      this.setNoticePeople(item)
    },
    // end
    setNoticePeople(selection) {
      if (selection.length === 0 || JSON.stringify(selection) === '{}') {
        this.formInline.noticeBtn = ''
        this.formInline.num = 0
      } else {
        const person = selection.map((item) => {
          return item.name + ',' + item.phone + ',' + item.wechat + ',' + item.staffId
        })
        this.formInline.personHidden = person.toString()
        this.formInline.num = selection.length
      }
    },
    // 服务人员 start
    closePeopleDialog() {
      this.changeTeamsPeopleShow = false
      this.setFormPeopleData(this.selectTeamPeopleRow)
    },
    peopleSure(item) {
      this.changeTeamsPeopleShow = false
      this.selectTeamPeopleRow = item
      this.setFormPeopleData(this.selectTeamPeopleRow)
    },
    // 根据id删除人员
    peopleDel(id) {
      this.selectTeamPeopleRow = this.selectTeamPeopleRow.filter((e) => e.id !== id)
      this.setFormPeopleData(this.selectTeamPeopleRow)
    },
    // 选中人员 提交数据重组   服务人员 end
    setFormPeopleData(selection) {
      if (selection.length) {
        // 注释eslint
        // eslint-disable-next-line
        const name = Array.from(selection, ({ member_name }) => member_name)
        const code = Array.from(selection, ({ id }) => id)
        const phone = Array.from(selection, ({ phone }) => phone)
        this.formInline.designatePersonCode = code.toString()
        this.formInline.designatePersonName = name.toString()
        this.formInline.designatePersonPhone = phone.toString()
      } else {
        this.formInline.designatePersonCode = ''
        this.formInline.designatePersonName = ''
        this.formInline.designatePersonPhone = ''
        this.formInline.designCheck = ''
      }
    },
    // 打开选择弹框（科室或服务部门）
    openDeptSelectDialog(type) {
      this.currentSelectType = type
      if (type === 'sourcesDept') {
        // 打开科室选择弹框
        this.sourceDeptSelectDialogVisible = true
        this.searchSourceDeptName = ''
        this.selectedSourceDeptValue = ''
        // 初始化科室列表
        this.filteredSourceDeptOptions = [...this.allSourceDeptOptions]
      } else {
        // 打开服务部门选择弹框
        this.deptSelectDialogVisible = true
        this.pageNo = 1
        this.searchCompanyId = ''
        this.searchTeamName = ''
        this.getCompanyList()
        this.getDeptList()
      }
    },
    // 科室选择相关方法
    updateFilteredSourceDeptOptions() {
      // 根据搜索条件过滤科室列表
      const filtered = this.sourcesDeptOptions.filter((item) => {
        if (!this.searchSourceDeptName) return true
        return item.label.toLowerCase().includes(this.searchSourceDeptName.toLowerCase())
      })
      this.filteredSourceDeptTotal = filtered.length
      // 分页处理
      const start = (this.sourceDeptPageNo - 1) * this.sourceDeptPageSize
      const end = start + this.sourceDeptPageSize
      this.filteredSourceDeptOptions = filtered.slice(start, end)
    },
    handleSourceDeptSearchChange() {
      this.sourceDeptPageNo = 1
      this.updateFilteredSourceDeptOptions()
    },
    resetSourceDeptSearch() {
      this.searchSourceDeptName = ''
      this.sourceDeptPageNo = 1
      this.updateFilteredSourceDeptOptions()
    },
    handleSourceDeptRowClick(row) {
      // 先清除所有选择
      this.$refs.sourceDeptTable.clearSelection()
      // 只选中当前行
      this.$refs.sourceDeptTable.toggleRowSelection(row, true)
      this.selectedSourceDept = row
    },
    handleSourceDeptSelectionChange(selection) {
      // 限制只能选择一个
      if (selection.length > 1) {
        // 保留最后选择的一个
        const lastSelected = selection[selection.length - 1]
        this.$refs.sourceDeptTable.clearSelection()
        this.$refs.sourceDeptTable.toggleRowSelection(lastSelected, true)
        this.selectedSourceDept = lastSelected
      } else if (selection.length === 1) {
        this.selectedSourceDept = selection[0]
      } else {
        this.selectedSourceDept = null
      }
    },
    handleSourceDeptSizeChange(val) {
      this.sourceDeptPageSize = val
      this.sourceDeptPageNo = 1
      this.updateFilteredSourceDeptOptions()
    },
    handleSourceDeptCurrentChange(val) {
      this.sourceDeptPageNo = val
      this.updateFilteredSourceDeptOptions()
    },
    // 科室选择相关方法
    filterSourceDepts() {
      // 根据搜索条件过滤科室列表
      if (!this.searchSourceDeptName) {
        this.filteredSourceDeptOptions = [...this.allSourceDeptOptions]
      } else {
        this.filteredSourceDeptOptions = this.allSourceDeptOptions.filter((item) => {
          return item.label.toLowerCase().includes(this.searchSourceDeptName.toLowerCase())
        })
      }
    },
    closeSourceDeptSelectDialog() {
      this.sourceDeptSelectDialogVisible = false
      this.selectedSourceDeptValue = ''
      this.searchSourceDeptName = ''
    },
    confirmSourceDeptSelect() {
      if (!this.selectedSourceDeptValue) {
        this.$message.warning('请选择一个科室')
        return
      }
      // 获取选中的科室信息
      const selectedDept = this.allSourceDeptOptions.find((item) => item.value === this.selectedSourceDeptValue)
      if (selectedDept) {
        // 设置所属科室的值
        const deptValue = selectedDept.value + '_' + selectedDept.label
        this.formInline.sourcesDept = deptValue
        // 将选中的科室添加到下拉列表选项中，确保能正常回显
        // 检查是否已存在该科室，避免重复添加
        const isExist = this.sourcesDeptOptions.some((item) => item.value === selectedDept.value)
        if (!isExist) {
          this.sourcesDeptOptions.push({
            value: selectedDept.value,
            label: selectedDept.label
          })
        }
        // 根据配置决定是否获取关联的服务地点
        // departmentServiceLocationCascade: 0-科室带出地点, 1-地点带出科室, 2-互相关联
        const cascadeType = this.olgWorkPushNew && this.olgWorkPushNew.departmentServiceLocationCascade
        if (cascadeType === '0' || cascadeType === '2') {
          // 调用接口获取科室关联的服务地点
          this.getSpaceInfoByDeptId(selectedDept.value)
        }
      }
      this.sourceDeptSelectDialogVisible = false
      this.selectedSourceDeptValue = ''
      this.searchSourceDeptName = ''
    },
    // 根据科室ID获取关联的服务地点
    getSpaceInfoByDeptId(deptId) {
      this.$api
        .getNewSpaceInfoByDeptId({ deptId })
        .then((res) => {
          if (res.code === 200 && res.data && res.data.spaceInfoByDeptIdList && res.data.spaceInfoByDeptIdList.length > 0) {
            const spaceList = res.data.spaceInfoByDeptIdList
            // 添加服务地点到下拉选项
            spaceList.forEach((space) => {
              // 生成location ID和name
              // ID需要把allParentId和id拼在一起用下划线拼接
              const locationId = space.allParentId && space.allParentId !== '#' ? space.allParentId.replace(/#,?/, '').replace(/,/g, '_') + '_' + space.id : space.id
              // 名称需要把大于号(>)替换成下划线(_)
              const locationName = space.allParentName ? space.allParentName.replace(/>/g, '_') + '_' + space.gridName : space.gridName
              // 检查是否已存在该服务地点，避免重复添加
              const isExist = this.locationOptions.some((item) => item.id === locationId)
              if (!isExist) {
                this.locationOptions.push({
                  id: locationId,
                  name: locationName
                })
              }
            })
            // 默认选中第一个服务地点
            if (spaceList.length > 0 && !this.formInline.localtion) {
              const firstSpace = spaceList[0]
              // ID需要把allParentId和id拼在一起用下划线拼接
              const locationId =
                firstSpace.allParentId && firstSpace.allParentId !== '#' ? firstSpace.allParentId.replace(/#,?/, '').replace(/,/g, '_') + '_' + firstSpace.id : firstSpace.id
              // 名称需要把大于号(>)替换成下划线(_)
              const locationName = firstSpace.allParentName ? firstSpace.allParentName.replace(/>/g, '_') + '_' + firstSpace.gridName : firstSpace.gridName
              this.formInline.localtion = locationId
              this.formInline.localtionName = locationName
            }
          }
        })
        .catch((err) => {
          console.error('获取科室关联的服务地点失败', err)
        })
    },
    // 服务部门选择相关方法
    // 获取公司列表
    getCompanyList() {
      const params = {
        pageNo: 1,
        pageSize: 999
      }
      this.$api.oneStopApi
        .getCompanyByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.companyOptions = res.data.rows || []
          } else {
            this.$message.error(res.msg || '获取公司列表失败')
          }
        })
        .catch((err) => {
          console.error('获取公司列表失败', err)
          this.$message.error('获取公司列表失败')
        })
    },
    // 查询条件变更
    handleSearchChange() {
      this.pageNo = 1
      this.getDeptList()
    },
    // 重置查询条件
    resetSearch() {
      this.searchCompanyId = ''
      this.searchTeamName = ''
      this.pageNo = 1
      this.getDeptList()
    },
    closeDeptSelectDialog() {
      this.deptSelectDialogVisible = false
      this.selectedDept = null
    },
    confirmDeptSelect() {
      if (!this.selectedDept) {
        this.$message.warning('请选择一个服务部门')
        return
      }
      // 检查选中的部门是否已存在于下拉选项中
      const isExist = this.teamsOptions.some((item) => item.id === this.selectedDept.id)
      // 如果不存在，则添加到下拉选项中
      if (!isExist) {
        this.teamsOptions.push({
          id: this.selectedDept.id,
          team_name: this.selectedDept.teamName
        })
      }
      // 设置服务部门的值并更新下拉框
      const deptValue = this.selectedDept.id + '_' + this.selectedDept.teamName
      this.formInline.designateDeptCode = deptValue
      this.deptChange() // 触发选择变更事件
      this.deptSelectDialogVisible = false
    },
    // 获取服务部门列表
    getDeptList() {
      const params = {
        pageSize: this.pageSize,
        pageNo: this.pageNo
      }
      // 添加查询条件
      if (this.searchCompanyId) {
        params.companyId = this.searchCompanyId
      }
      if (this.searchTeamName) {
        params.teamName = this.searchTeamName
      }
      this.$api
        .getOrderDeptList(params)
        .then((res) => {
          if (res.code === '200') {
            this.deptTableData = res.data.rows || []
            this.totalCount = res.data.total || 0
          } else {
            this.$message.error(res.msg || '获取服务部门列表失败')
          }
        })
        .catch((err) => {
          console.error('获取服务部门列表失败', err)
          this.$message.error('获取服务部门列表失败')
        })
    },
    // 处理表格行点击事件
    handleDeptRowClick(row) {
      // 先清除所有选择
      this.$refs.deptTable.clearSelection()
      // 只选中当前行
      this.$refs.deptTable.toggleRowSelection(row, true)
      this.selectedDept = row
    },
    // 处理表格选择变更事件
    handleDeptSelectionChange(selection) {
      // 限制只能选择一个
      if (selection.length > 1) {
        // 保留最后选择的一个
        const lastSelected = selection[selection.length - 1]
        this.$refs.deptTable.clearSelection()
        this.$refs.deptTable.toggleRowSelection(lastSelected, true)
        this.selectedDept = lastSelected
      } else if (selection.length === 1) {
        this.selectedDept = selection[0]
      } else {
        this.selectedDept = null
      }
    },
    // 计算表格序号
    getTableIndex(index) {
      return (this.pageNo - 1) * this.pageSize + index + 1
    },
    // 处理每页显示条数变更
    handleSizeChange(val) {
      this.pageSize = val
      this.pageNo = 1
      this.getDeptList()
    },
    // 处理页码变更
    handleCurrentChange(val) {
      this.pageNo = val
      this.getDeptList()
    },
    // 控制选择框是否可选
    handleSelectable(row, index) {
      // 允许选择，但我们会在selection-change事件中限制只能选一个
      return true
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  .content-top {
    padding: 10px 15px 10px 5px;
    background: #fff;
    > div {
      display: flex;
      height: 32px;
      line-height: 32px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      .work-order-label {
        width: 80px;
        color: $color-text;
        text-align: right;
      }
      ::v-deep .el-input__inner {
        width: 200px;
      }
      .work-order-value {
        flex: 1;
        padding-left: 10px;
        color: $color-text;
        font-weight: 600;
      }
    }
  }
  .dept-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .dept-form-item {
    margin-right: 40px !important;
    ::v-deep .el-form-item__label {
      white-space: nowrap;
    }
  }
  .person-form-item {
    margin-left: 40px !important;
  }
  .dept-select-container,
  .location-select-container {
    display: flex;
    align-items: center;
    width: 100%;
    .el-select {
      flex: 1;
      min-width: 200px;
      margin-right: 15px;
      ::v-deep .el-input {
        width: 100%;
      }
    }
    .el-button {
      flex-shrink: 0;
      min-width: 60px;
    }
  }
  .content-footer {
    padding: 10px;
    height: calc(100% - 40px);
    .box-card {
      padding: 12px;
    }
    ::v-deep .card-title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .card-name {
        font-size: 16px;
        span {
          color: #f56c6c !important;
        }
      }
    }
    ::v-deep .card-body {
      height: calc(100% - 38px);
      .footer-left {
        height: 100%;
        margin-right: 3%;
        .el-tree {
          height: calc(100% - 120px);
          overflow: auto;
        }
      }
    }
    .select-servive {
      font-family: PingFangSC-Regular, 'PingFang SC';
      font-size: 14px;
      font-weight: 600;
      color: $color-primary;
      padding-left: 0;
    }
    .footer-right {
      ::v-deep .el-form-item__label {
        font-size: 16px;
      }
      ::v-deep .el-form-item__content span {
        font-size: 16px;
      }
      ::v-deep .form-data {
        height: calc(100% - 30px);
        overflow-y: scroll;
        .formRow {
          display: flex;
          width: 75%;
          .el-form-item {
            flex: 1;
            display: flex;
            margin-bottom: 18px;
            .el-form-item__error {
              padding-top: 2px;
              position: static;
            }
          }
          &.service-time-row {
            display: flex;
            align-items: center;
            .appointment-type {
              margin-right: 20px;
              margin-bottom: 0;
              white-space: nowrap;
            }
            .appointment-date {
              margin-bottom: 0;
            }
          }
          .el-form-item__content {
            flex: 1;
            .el-select {
              width: 100%;
            }
          }
          .phone-item {
            .el-form-item__label-wrap {
              margin-left: 28px !important;
            }
          }
        }
        .margin-bottom-none {
          .el-form-item {
            margin-bottom: 0;
          }
        }
        .repair-work {
          width: 95%;
          .el-form-item__content {
            text-align: right;
          }
        }
      }
      .maint-table {
        width: 70%;
        margin: 0 0 20px 80px;
        border: 1px solid $color-text-secondary;
        border-collapse: collapse;
        td {
          padding: 5px 0 5px 10px;
          border: 1px solid $color-text-secondary;
          height: 25px;
          line-height: 25px;
          vertical-align: middle;
        }
        tr:first-child {
          background-color: $color-text-secondary;
        }
        td:first-child {
          width: 35%;
        }
        .one-line {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .scope-del {
          color: $color-primary;
          cursor: pointer;
        }
      }
    }
  }
}
::v-deep .el-table-column--selection .cell {
  padding: 0 10px;
  text-overflow: initial;
}
// 服务事项TOP5标签样式
.item-top5-tags {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .el-tag.el-tag--light {
    color: $color-primary !important;
  }
  .tag-label {
    margin-right: 8px;
    color: #606266;
    font-size: 12px;
  }
  .top5-tag {
    font-size: 14px;
    margin-right: 8px;
    margin-bottom: 5px;
    cursor: pointer;
    &:hover {
      color: $color-primary;
      border-color: $color-primary;
    }
  }
}
</style>
<style>
/* 全局样式 */
.dept-select-dialog {
  z-index: 3000 !important; /* 确保弹框显示在最上层 */
}
.right-tips {
  width: auto;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #f56c6c;
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-left: 16px;
  font-size: 16px;
}
.right-tips .v-img {
  margin-right: 10px;
  display: inline-block;
  height: 20px;
  width: 20px;
  background: center url('~@/assets/images/workOrder/volume.png') no-repeat;
  background-size: 100% 100%;
}
.search-container {
  margin-bottom: 15px;
}
.search-form .el-form-item {
  margin-bottom: 10px;
}
.search-form .el-select {
  width: 220px;
}
.search-form .el-input {
  width: 220px;
}
.dept-select-dialog .dept-pagination {
  margin-top: 10px;
  text-align: right;
}
.dept-select-dialog .el-dialog__body {
  padding-bottom: 10px;
}
.dept-container {
  display: flex;
  flex-direction: column;
  height: 350px;
}
.dept-list-container {
  margin-top: 15px;
  height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
}
.dept-radio-group {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.dept-radio-item {
  width: calc(50% - 10px);
  margin-right: 10px;
  margin-bottom: 5px;
  padding: 8px 10px;
  display: inline-block;
  box-sizing: border-box;
}
.dept-radio-item span {
  font-size: 16px;
}
/* 附件展示相关样式 */
.desc-attachment-row {
  display: flex !important;
  width: 100% !important;
  margin-bottom: 15px;
}

.description-item {
  width: 70%;
  margin-right: 15px !important;
}

.attachment-container {
  width: 32%;
  display: flex;
  flex-direction: column;
  margin-top: 4px;
}

.attachment-title {
  color: #606266;
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
  text-align: left;
}

.attachment-content {
  display: flex;
  flex-direction: column;
  gap: 0;
  height: 100%;
}

.section-row {
  display: flex;
  align-items: center;
  width: 100%;
}

.attachment-label {
  padding-left: 12px;
  color: #121f3e;
  white-space: nowrap;
  margin-right: 5px;
  line-height: 28px;
}

.audio-container {
  margin-bottom: 5px;
}

.audio-file {
  display: flex;
  align-items: center;
  padding: 0px;
  border-radius: 4px;
  flex: 1;
}

.file-name {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #606266;
}

.audio-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: 8px;
}

.action-icon {
  font-size: 18px;
  color: #3562db;
  cursor: pointer;
}

.action-icon:hover {
  color: #409eff;
}

.image-section {
  margin-top: 0px;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 3px;
  flex: 1;
}

.attachment-image {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  object-fit: cover;
}
.tree .el-tree-node__label {
  font-size: 16px !important;
}
</style>
