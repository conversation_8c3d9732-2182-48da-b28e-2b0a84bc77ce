<template>
  <el-dialog
    v-dialogDrag
    v-dialogResize="resizeOptions"
    :modal="useModal"
    custom-class="model-dialog"
    width="58%"
    append-to-body
    :visible="workOrderDealShow"
    :close-on-click-modal="false"
    :before-close="closeDialog"
    :fullscreen="isFullscreen"
    @close="closeDialog"
  >
    <template slot="title">
      <div class="dialog-header-content">
        <span class="dialog-title">{{
          dealType === 'dealToAdd'
            ? '暂存工单处理'
            : (dealType === 'add' ? '新增' : '') + workTypeName + (dealType === 'deal' ? '（ 处理 ）' : '') + (dealType === 'update' ? '（ 修改 ）' : '')
        }}</span>
        <div class="dialog-tools">
          <i :class="['el-icon', isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen']" @click="toggleFullScreen"></i>
        </div>
      </div>
      <!-- right-tips已移至来电号码位置 -->
      <!-- <div class="right-tips"><span class="v-img"></span>{{ workOrderDetail.taskWorkHint }}</div> -->
    </template>
    <olgMaintenance
      v-if="workOrderDealShow && workOrderDetail?.olgTaskManagement?.workTypeCode"
      ref="olgMaintenance"
      :projectCode="projectCode"
      :dealType="dealType"
      :alarmId="alarmId"
      :spaceId="spaceId"
      routerFrom="local"
      :workOrderDetail="workOrderDetail"
      @save="getSaveCallback"
    />
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="isSubmitting" @click="savePeople">{{ dealType === 'update' ? '确 定' : '派 工' }}</el-button>
      <el-button v-if="dealType === 'add' || dealType === 'dealToAdd'" type="primary" :loading="isSubmitting" @click="saveTemporary">暂 存</el-button>
      <el-button type="primary" plain @click="closeDialog">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import olgMaintenance from './components/olgMaintenance.vue'
export default {
  name: 'CreatedWorkOrder',
  components: {
    olgMaintenance
  },
  props: {
    workOrderDealShow: {
      type: Boolean,
      default: false
    },
    dealType: {
      type: String,
      default: 'add'
    },
    alarmId: {
      type: String,
      default: ''
    },
    spaceId: {
      type: String,
      default: ''
    },
    projectCode: {
      type: String,
      default: ''
    },
    workTypeCode: {
      type: String,
      default: ''
    },
    workTypeName: {
      type: String,
      default: ''
    },
    workTypeId: {
      type: String,
      default: ''
    },
    // 是否启用遮罩层
    useModal: {
      type: Boolean,
      default: false
    },
    // 是否应该初始化数据
    shouldInitData: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      workOrderDetail: {},
      isSubmitting: false, // 防抖标志
      isFullscreen: false, // 全屏状态
      prevDialogStyle: null // 用于保存对话框的原始位置和缩放状态
    }
  },
  computed: {
    // 拖拽放大配置选项
    resizeOptions() {
      return {
        minWidth: 400,
        minHeight: 300,
        maxWidth: window.innerWidth * 0.95,
        maxHeight: window.innerHeight * 0.95,
        onResize: this.handleDialogResize
      }
    }
  },
  watch: {
    // 监听属性变化仍然保留，以防万一
    workOrderDealShow(newVal) {
      if (newVal && this.workTypeCode) {
        this.initData()
      }
    }
  },
  created() {},
  mounted() {
    // 由于使用了v-if，每次显示时组件都会重新挂载，所以可以在mounted中初始化
    if (this.workOrderDealShow && this.workTypeCode) {
      this.initData()
    }
  },
  methods: {
    initData() {
      if (this.dealType === 'deal' || this.dealType === 'update' || this.dealType === 'dealToAdd') {
        this.getWorkOrderOper()
      } else {
        this.getWorkOrderToAdd()
      }
    },
    getWorkOrderOper() {
      const params = {
        id: this.workTypeId,
        operType: 'placeOrder'
        // ...__PATH.USER_CODE
      }
      this.$api.getWorkOrderOper(params).then((res) => {
        this.workOrderDetail = res
      })
    },
    getWorkOrderToAdd() {
      const params = {
        type: 1,
        workTypeCode: this.workTypeCode,
        workTypeName: this.getWorkTypeLetter(this.workTypeCode)
      }
      this.$api.getWorkOrderToAdd(params).then((res) => {
        this.workOrderDetail = res
      })
    },
    getWorkTypeLetter(code) {
      switch (code) {
        case '1':
          return 'WX'
        case '2':
          return 'BJ'
      }
    },
    // 取消按钮
    closeDialog() {
      this.$emit('update:workOrderDealShow', false)
      // 清空数据
      this.workOrderDetail = {}
      // 清理拖拽放大的样式类
      this.$nextTick(() => {
        const dialogWrap = document.querySelector('.model-dialog')
        if (dialogWrap) {
          dialogWrap.classList.remove('dialog-resized')
        }
      })
    },
    // 确认按钮（带防抖）
    savePeople() {
      if (this.isSubmitting) return // 如果正在提交，则不再执行
      this.isSubmitting = true // 设置提交状态为true
      this.$refs.olgMaintenance.saveForm()
      // 3秒后重置提交状态（保守设置，确保接口有足够时间响应）
      setTimeout(() => {
        this.isSubmitting = false
      }, 3000)
    },
    // 暂存按钮（带防抖）
    saveTemporary() {
      if (this.isSubmitting) return // 如果正在提交，则不再执行
      this.isSubmitting = true // 设置提交状态为true
      this.$refs.olgMaintenance.saveForm({ isTemporary: '1' })
      // 3秒后重置提交状态
      setTimeout(() => {
        this.isSubmitting = false
      }, 3000)
    },
    getSaveCallback(item) {
      // 关联工单返回数据 保存关联报警
      // if (item) {
      //   this.$api.limWorkInfo(item).then((res) => {
      //     console.log(res)
      //   })
      // }
      this.$emit('workOrderSure', item)
      // 清空数据
      this.workOrderDetail = {}
      // 保存完成后重置提交状态
      this.isSubmitting = false
    },
    // 全屏切换方法
    toggleFullScreen() {
      this.isFullscreen = !this.isFullscreen
      // 重置对话框位置，解决拖拽后全屏位置错误的问题
      this.$nextTick(() => {
        const dialogEl = document.querySelector('.model-dialog .el-dialog')
        const dialogWrap = document.querySelector('.model-dialog')

        if (dialogEl && dialogWrap) {
          if (this.isFullscreen) {
            // 保存当前位置，以便退出全屏时恢复
            this.prevDialogStyle = {
              top: dialogEl.style.top,
              left: dialogEl.style.left,
              margin: dialogEl.style.margin,
              transform: dialogEl.style.transform,
              position: dialogEl.style.position,
              width: dialogEl.style.width,
              height: dialogEl.style.height
            }

            // 完全重置位置样式，确保在全屏模式下正确居中
            dialogEl.style.top = ''
            dialogEl.style.left = ''
            dialogEl.style.transform = ''
            dialogEl.style.margin = '15vh auto 0'
            dialogEl.style.position = ''
            dialogEl.style.width = ''
            dialogEl.style.height = ''

            // 添加全屏样式类，用于控制内容区域高度
            dialogWrap.classList.add('is-fullscreen')
            // 全屏时移除拖拽放大的样式类，因为全屏有自己的高度控制
            dialogWrap.classList.remove('dialog-resized')

            // 调整父元素定位
            dialogWrap.style.position = 'fixed'
            dialogWrap.style.top = '0'
            dialogWrap.style.left = '0'
            dialogWrap.style.right = '0'
            dialogWrap.style.bottom = '0'
          } else {
            // 恢复所有之前的样式属性
            if (this.prevDialogStyle) {
              for (const prop in this.prevDialogStyle) {
                dialogEl.style[prop] = this.prevDialogStyle[prop]
              }
            }

            // 移除全屏样式类
            dialogWrap.classList.remove('is-fullscreen')

            // 恢复对话框容器的样式
            dialogWrap.style.position = ''
            dialogWrap.style.top = ''
            dialogWrap.style.left = ''
            dialogWrap.style.right = ''
            dialogWrap.style.bottom = ''
          }
        }
      })
    },

    // 处理对话框拖拽放大事件
    handleDialogResize(size) {
      // 可以在这里处理拖拽放大后的逻辑
      // 比如调整内部组件的尺寸等
      console.log('Dialog resized:', size)

      // 注意：样式类的添加现在由指令自动处理，无需手动添加
      // 这里可以添加其他需要在尺寸变化时执行的逻辑
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  .dialog-footer span {
    font-size: 16px;
  }
  .el-dialog__header {
    display: flex;
    padding: 20px 20px 10px;

    .dialog-header-content {
      display: flex;
      align-items: center;
      width: 100%;

      .dialog-title {
        display: inline-block;
        width: 38%;
      }

      .dialog-tools {
        margin-left: auto;
        margin-right: 40px;
        display: flex;
        align-items: center;
        i {
          transform: translateY(-2px);
          cursor: pointer;
          font-size: 16px;
          line-height: 1;
          &:hover {
            color: #409eff;
          }
        }
      }
    }

    .right-tips {
      width: 50%;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #f56c6c;
      display: flex;
      justify-content: center;
      .v-img {
        margin: auto 0;
        margin-right: 10px;
        display: inline-block;
        height: 20px;
        width: 20px;
        background: center url('~@/assets/images/workOrder/volume.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }
  .el-dialog__body {
    padding: 0 0 15px;
    max-height: calc(70vh - 110px);
    overflow: auto;
  }

  // 当对话框被手动调整大小时，内容区域应该自适应
  &.dialog-resized {
    .el-dialog__body {
      max-height: none !important;
      height: calc(100% - 110px) !important;
      overflow: auto;
    }
  }

  // 全屏模式下的样式调整
  &.is-fullscreen {
    display: flex;
    flex-direction: column;
    height: 100% !important;

    .el-dialog__body {
      flex: 1;
      overflow: auto;
      max-height: none;
      height: calc(100% - 110px);
    }

    // 全屏时隐藏拖拽手柄
    .dialog-resize-handle {
      display: none !important;
    }
  }

  // 拖拽手柄样式优化
  .dialog-resize-handle {
    &:hover {
      &::after {
        content: '';
        position: absolute;
        right: 0;
        bottom: 0;
        width: 12px;
        height: 12px;
        background: linear-gradient(-45deg, transparent 30%, #409eff 30%, #409eff 40%, transparent 40%, transparent 60%, #409eff 60%, #409eff 70%, transparent 70%);
      }
    }
  }
}
.el-table-column--selection .cell {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
  text-overflow: initial;
}
</style>
