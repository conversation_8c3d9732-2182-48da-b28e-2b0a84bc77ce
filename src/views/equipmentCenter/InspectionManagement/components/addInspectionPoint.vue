<template>
  <el-dialog
    v-if="visible"
    :class="dialogContentType == 'customize' ? 'customizeDialog' : 'spaceDialog'"
    :title="systemType == '2' ? '保养点选择' : '巡检点选择'"
    :visible="visible"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    top="8vh"
    width="80%"
    :before-close="
      () => {
        $emit('close')
      }
    "
  >
    <div class="dialogContent">
      <div class="topMenu">
        <el-tabs v-model="activeName" @tab-click="changeActive">
          <el-tab-pane v-if="systemType != '2'" label="按空间" name="space"></el-tab-pane>
          <el-tab-pane v-if="systemType != '3'" label="按设备" name="equipment"></el-tab-pane>
          <el-tab-pane label="自定义" name="customize"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="pointListWrap">
        <div v-if="activeName == 'space' || activeName == 'equipment'" class="spaceContent">
          <div class="leftTree">
            <div class="toptip" style="padding-left: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              {{ activeName == 'space' ? '空间结构' : '设备分类' }}
            </div>
            <el-input v-model="filterText" placeholder="输入关键字"> </el-input>
            <!-- <el-tree
              ref="tree"
              :data="treeData"
              :props="treeProps"
              :highlight-current="true"
              node-key="id"
              :filter-node-method="filterNode"
              @node-click="nodeClick">
            </el-tree> -->
            <ZkRenderTree
              ref="tree"
              :data="treeData"
              :props="treeProps"
              :highlight-current="true"
              node-key="id"
              :filter-node-method="filterNode"
              @node-click="nodeClick"
            ></ZkRenderTree>
          </div>
          <div class="rightTable">
            <div v-if="activeName == 'space'" class="topFilter">
              <div class="spaceFilter">
                <!-- <el-select v-model="spaceFilter.functionDictId" placeholder="请选择空间功能用途" @keyup.enter.native="spaceSearch">
                  <el-option v-for="item in spaceFunctionType" :key="item.id" :label="item.dictName" :value="item.id"> </el-option>
                </el-select> -->
                <el-input v-model="spaceFilter.localSpaceName" placeholder="空间名称" @keyup.enter.native="spaceSearch"></el-input>
                <!-- <el-input v-model="spaceFilter.localSpaceCode" placeholder="本地编码" @keyup.enter.native="spaceSearch"></el-input> -->
                <el-select v-model="spaceFilter.spaceGroupId" placeholder="全部空间分组" style="margin: 0 10px">
                  <el-option v-for="item in spaceGroupList" :key="item.dictionaryDetailsId" :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsId"></el-option>
                </el-select>
                <el-cascader
                  v-model="spaceFilter.deptId"
                  :options="deptList"
                  :props="deptTreeProps"
                  placeholder="全部归属部门"
                  style="margin-right: 10px"
                  clearable
                  filterable
                  :show-all-levels="false"
                ></el-cascader>
                <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="spaceReset">重置</el-button>
                <el-button type="primary" @click="spaceSearch">查询</el-button>
              </div>
            </div>
            <div v-if="activeName == 'equipment'" class="topFilter">
              <div class="deviceFilter">
                <el-input v-model="deviceFilter" placeholder="设备名称" @keyup.enter.native="deviceSearch"></el-input>
                <el-cascader
                  ref="regionCode"
                  v-model="regionCode"
                  :props="riskPropsType"
                  :options="regionCodeList"
                  placeholder="全部空间位置"
                  class="cascaderWid"
                  filterable
                  :show-all-levels="false"
                ></el-cascader>
                <el-select v-model="spaceGroupFilter" placeholder="全部空间分组" style="margin-right: 10px">
                  <el-option v-for="item in spaceGroupList" :key="item.dictionaryDetailsId" :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsId"></el-option>
                </el-select>
                <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="deviceReset">重置</el-button>
                <el-button type="primary" @click="deviceSearch">查询</el-button>
              </div>
            </div>
            <div class="selected">
              <span>已选择</span>
              <div class="tagsWrap">
                <el-tag v-for="tag in tags" :key="tag.id" type="info" closable @close="deleteTag(tag.id)">
                  {{ activeName == 'space' ? tag.localSpaceName : tag.assetName }}
                </el-tag>
              </div>
            </div>
            <div class="table">
              <el-table
                v-if="activeName == 'space'"
                ref="spaceTable"
                v-loading="spaceLoading"
                :data="spaceTableData"
                style="width: 100%"
                height="265"
                border
                @select="selectRow"
                @select-all="selectAll"
              >
                <el-table-column align="center" type="selection" width="55"> </el-table-column>
                <el-table-column type="index" label="序号" width="60"> </el-table-column>
                <el-table-column prop="localSpaceName" label="空间名称" width="180" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="roomCode" label="房间编号" width="90" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="hospital" label="医院" width="120" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="area" label="院区" width="120" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="building" label="建筑" width="120" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="floor" label="楼层" width="120" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="roomSpaceTypeName" label="空间分组" width="150" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="dmName" label="归属部门" show-overflow-tooltip> </el-table-column>
              </el-table>
              <el-table
                v-if="activeName == 'equipment'"
                ref="deviceTable"
                v-loading="spaceLoading"
                :data="spaceTableData"
                style="width: 100%"
                height="265"
                border
                @select="selectRow"
                @select-all="selectAll"
              >
                <el-table-column align="center" type="selection" width="55"> </el-table-column>
                <el-table-column type="index" label="序号" width="60"> </el-table-column>
                <el-table-column prop="assetName" label="设备名称" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="assetCode" label="设备编码" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="hospital" label="医院" width="120" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="area" label="院区" width="120" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="building" label="建筑" width="120" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="floor" label="楼层" width="120" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="room" label="空间" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="roomSpaceTypeName" label="空间分组" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="assetStatusName" label="设备状态" show-overflow-tooltip> </el-table-column>
                <!-- <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-link type="primary" @click="detail(scope.row)">详情</el-link>
                  </template>
                </el-table-column> -->
              </el-table>
              <div class="" style="padding-top: 10px; text-align: right">
                <el-pagination
                  layout="total, sizes, prev, pager, next, jumper"
                  :current-page="paginationData.currentPage"
                  :page-sizes="[10, 30, 50, 100]"
                  :page-size="paginationData.pageSize"
                  :total="paginationData.total"
                  @size-change="sizeChange"
                  @current-change="currentChange"
                ></el-pagination>
              </div>
            </div>
          </div>
        </div>
        <div v-if="activeName == 'customize'" class="customizeContent">
          <div class="topBaseInfo">
            <div class="toptip" style="padding-left: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              基本信息
            </div>
            <el-form ref="form" :model="customizeForm" :rules="rules" label-width="100px">
              <el-row :gutter="50">
                <el-col :span="8">
                  <el-form-item :label="systemType == '2' ? '保养点名称' : '巡检点名称'" prop="name">
                    <el-input v-model="customizeForm.name" :placeholder="systemType == '2' ? '请输入保养点名称' : '请输入巡检点名称'" maxlength="20" show-word-limit></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="systemType == '2' ? '保养点编码' : '巡检点编码'" prop="code">
                    <el-input v-model="customizeForm.code" :placeholder="systemType == '2' ? '请输入保养点编码' : '请输入巡检点编码'" maxlength="20" show-word-limit></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="NFC" prop="nfcCode">
                    <el-input
                      v-model="customizeForm.nfcCode"
                      placeholder="请输入NFC码 "
                      maxlength="100"
                      onkeyup="this.value=this.value.replace(/[^\w]/g,'')"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="备注说明" prop="description">
                    <el-input
                      v-model="customizeForm.description"
                      type="textarea"
                      :placeholder="systemType == '2' ? '请输入保养点描述，最多200字' : '请输入巡检点描述，最多200字'"
                      resize="none"
                      :autosize="{ minRows: 3, maxRows: 5 }"
                      maxlength="200"
                      show-word-limit
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8" style="height: 75px; padding-left: 0; display: flex; align-items: end">
                  <el-button type="primary" @click="addZdyInspectionPoint">创建</el-button>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div class="customizePointList">
            <div class="zdyTopTitle">
              <div class="toptip" style="padding-left: 0; border: 0; font-weight: bold">
                <span class="green_line"></span>
                {{ systemType == '2' ? '自定义保养点列表' : '自定义巡检点列表' }}
              </div>
              <div class="selected">
                <span>已选择</span>
                <div class="tagsWrap">
                  <el-tag v-for="tag in tags" :key="tag.id" type="info" closable @close="deleteTag(tag.id)">
                    {{ tag.taskPointName }}
                  </el-tag>
                </div>
              </div>
            </div>
            <div class="table">
              <el-table ref="zdyTable" :data="zdyTableData" style="width: 100%" height="265" border @select="selectRow" @select-all="selectAll">
                <el-table-column align="center" type="selection" width="55"> </el-table-column>
                <el-table-column type="index" label="序号" width="80"> </el-table-column>
                <el-table-column prop="taskPointName" :label="systemType == '2' ? '保养点名称' : '巡检点名称'">
                  <template slot-scope="scope">
                    <el-input v-if="scope.row.isEdit" v-model="scope.row.taskPointName" maxlength="20" show-word-limit></el-input>
                    <span v-else>{{ scope.row.taskPointName }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="remarks" label="备注说明" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-input v-if="scope.row.isEdit" v-model="scope.row.remarks" type="textarea" maxlength="200" show-word-limit></el-input>
                    <span v-else>{{ scope.row.remarks }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="taskPointCode" label="编码 ">
                  <template slot-scope="scope">
                    <el-input v-if="scope.row.isEdit" v-model="scope.row.taskPointCode" maxlength="20" show-word-limit></el-input>
                    <span v-else>{{ scope.row.taskPointCode }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="nfcCode" label="NFC">
                  <template slot-scope="scope">
                    <el-input
                      v-if="scope.row.isEdit"
                      v-model.trim="scope.row.nfcCode"
                      maxlength="100"
                      onkeyup="this.value=this.value.replace(/[^\w]/g,'')"
                      show-word-limit
                    ></el-input>
                    <span v-else>{{ scope.row.nfcCode }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template slot-scope="scope">
                    <el-popconfirm title="确定删除此巡检点？" @confirm="deleteZdy(scope.row)">
                      <el-link slot="reference" type="primary" style="margin-right: 10px">删除</el-link>
                    </el-popconfirm>
                    <el-link v-if="scope.row.isEdit" type="primary" @click="zdyUpdata(scope.row)">保存</el-link>
                    <el-link v-else type="primary" @click="editZdy(scope.row)">编辑</el-link>
                  </template>
                </el-table-column>
              </el-table>
              <div class="" style="padding-top: 10px; text-align: right">
                <el-pagination
                  layout="total, sizes, prev, pager, next, jumper"
                  :current-page="zdyPaginationData.currentPage"
                  :page-sizes="[10, 30, 50, 100]"
                  :page-size="zdyPaginationData.pageSize"
                  :total="zdyPaginationData.total"
                  @size-change="zdySizeChange"
                  @current-change="zdyCurrentChange"
                ></el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="$emit('close')">取 消</el-button>
      <el-button type="primary" @click="checkConfirm">确 定</el-button>
    </span>
    <div>
      <el-dialog title="设备详情" :visible.sync="dialogDateilVisible" :append-to-body="true" :modal-append-to-body="false" class="inspectionPointDateil" top="18vh" width="55%">
        <div>
          <div class="detailDialog">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              基本信息
            </div>
            <el-row :gutter="60">
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备名称:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetName }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备编码:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetCode }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备ID:</div>
                <div class="contenWrap">{{ deviceRowDetail.id }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">品牌:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetBrand }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">型号:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetModel }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">出厂日期:</div>
                <div class="contenWrap">{{ moment(deviceRowDetail.dateOfManufacture).format('YYYY-MM-DD') }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">SN码:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetSn }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">启用日期:</div>
                <div class="contenWrap">{{ moment(deviceRowDetail.startDate).format('YYYY-MM-DD') }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">使用期限:</div>
                <div class="contenWrap">{{ deviceRowDetail.serviceLife + '月' }}</div>
              </el-col>
              <el-col :span="12" class="cosRow">
                <div class="titleWrap">所在区域:</div>
                <div class="contenWrap">{{ deviceRowDetail.regionName }}</div>
              </el-col>
              <el-col :span="12" class="cosRow">
                <div class="titleWrap">使用状态:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetStatusName }}</div>
              </el-col>
            </el-row>
          </div>
          <div class="detailDialog typeInfo">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              类别信息
            </div>
            <el-row :gutter="60">
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">归口部门:</div>
                <div class="contenWrap">{{ deviceRowDetail.centralizedDepartmentName }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">归属系统:</div>
                <div class="contenWrap">{{ deviceRowDetail.systemCategoryName }}</div>
              </el-col>
            </el-row>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="dialogDateilVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
import moment from 'moment'
export default {
  components: {},
  model: {
    prop: 'visible',
    event: 'close'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      moment,
      systemType: '',
      dialogDateilVisible: false,
      spaceLoading: false,
      activeName: 'space',
      dialogContentType: 'space',
      allSpaceTreeData: [], // 未处理的空间树结构
      treeData: [], // 树数据来源
      spaceFunctionType: [], // 空间功能类型
      deptList: [], // 部门列表
      deptTreeProps: {
        children: 'children',
        label: 'deptName',
        value: 'id',
        checkStrictly: false,
        multiple: false,
        disabled: true,
        emitPath: false
      },
      checkItem: '', // 当前树选中项
      spaceFilter: {
        functionDictId: '', // 已选功能类型
        localSpaceName: '', // 空间本地名称
        localSpaceCode: '', // 本地编码
        spaceGroupId: '', // 空间分组
        deptId: '' // 归属部门
      },
      spaceTableData: [], // 空间设备巡检点列表
      zdyTableData: [], // 自定义巡检点列表
      treeProps: {
        children: 'children',
        label: (data, node) => {
          if (this.activeName == 'space') {
            return data.ssmName || ''
          } else {
            const name = data.baseName || data.dictName || ''
            return name + (data.dataSum ? `  (${data.dataSum})` : '')
          }
        },
        isLeaf: 'leaf'
      },
      filterText: '',
      deviceTree: [], // 设备分类
      deviceRowDetail: {}, // 设备详情
      deviceFilter: '',
      regionCode: [],
      spaceGroupFilter: '',
      spaceGroupList: [],
      riskPropsType: {
        children: 'children',
        label: 'ssmName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      regionCodeList: [],
      deviceProps: {
        label: 'baseName',
        isLeaf: 'leaf',
        children: 'children'
      },
      tags: [],
      // 空间设备分页
      paginationData: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      // 自定义分页
      zdyPaginationData: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      // 创建自定义巡检点
      customizeForm: {
        name: '',
        code: '',
        description: '',
        nfcCode: ''
      },
      rules: {
        name: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                if (this.systemType == '2') {
                  callback(new Error('请输入保养点名称'))
                } else {
                  callback(new Error('请输入巡检点名称'))
                }
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                if (this.systemType == '2') {
                  callback(new Error('请输入保养点编码'))
                } else {
                  callback(new Error('请输入巡检点编码'))
                }
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      // 选中的自定义巡检点
      selectedZdyPoint: []
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.getTreeRef().filter(val)
    },
    visible(val) {
      if (val) {
        this.tags = []
        if (this.$refs.spaceTable) {
          this.$refs.spaceTable.clearSelection()
        }
        if (this.$refs.deviceTable) {
          this.$refs.deviceTable.clearSelection()
        }
        if (this.$refs.zdyTable) {
          this.$refs.zdyTable.clearSelection()
        }
        this.$nextTick(() => {
          this.$refs.tree.getTreeRef().setCurrentKey(this.checkItem)
        })
      }
    }
  },
  created() {
    if (this.$route.path.indexOf('/InspectionManagement') != -1) {
      this.systemType = '1'
      this.getSpaceTree()
      this.getFunctionType()
    } else if (this.$route.path.indexOf('/MaintenanceManagement') != -1) {
      this.systemType = '2'
      this.activeName = 'equipment'
      this.getDeviceType()
    } else if (this.$route.path.indexOf('/comInspectionManagement') != -1) {
      this.systemType = '3'
      this.getSpaceTree()
      this.getFunctionType()
    }
    this.getRegionList()
    this.getSpaceGroupList()
    this.getDeptList()
  },
  mounted() {},
  methods: {
    changeActive(tab, event) {
      this.tags = []
      this.paginationData = {
        pageSize: 10,
        currentPage: 1,
        total: 0
      }
      // 自定义分页
      this.zdyPaginationData = {
        pageSize: 10,
        currentPage: 1,
        total: 0
      }
      // 创建自定义巡检点
      this.customizeForm = {
        name: '',
        code: '',
        description: '',
        nfcCode: ''
      }
      this.checkItem = ''
      if (tab.name == 'customize') {
        this.dialogContentType = 'customize'
        this.getZdyInspectionPoint()
      } else {
        this.dialogContentType = 'space'
        if (tab.name == 'equipment') {
          this.getDeviceType()
        } else {
          this.getSpaceTree()
          this.getFunctionType()
        }
      }
    },
    filterNode(value, data) {
      if (this.activeName == 'space') {
        if (!value) return true
        return data.ssmName.indexOf(value) !== -1
      } else if (this.activeName == 'equipment') {
        if (!value) return true
        return data.baseName.indexOf(value) !== -1
      }
    },
    // 空间tree
    getSpaceTree() {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == '200') {
          this.allSpaceTreeData = res.data
          this.treeData = transData(res.data, 'id', 'pid', 'children')
          this.checkItem = this.treeData[0].id
          if (this.visible) {
            this.$nextTick(() => {
              this.$refs.tree.getTreeRef().setCurrentKey(this.checkItem)
            })
          }
          this.getSpaceList(this.checkItem)
        }
      })
    },
    // 空间功能类型
    getFunctionType() {
      this.$api
        .getFunctionType({
          typeValue: 'SP'
        })
        .then((res) => {
          if (res.code == '200') {
            this.spaceFunctionType = res.data
          }
        })
    },
    // 空间巡检点列表
    getSpaceList(treeId) {
      this.spaceLoading = true
      let data = {
        current: this.paginationData.currentPage,
        functionDictId: this.spaceFilter.functionDictId,
        localSpaceName: this.spaceFilter.localSpaceName,
        localSpaceCode: this.spaceFilter.localSpaceCode,
        simCode: '#,' + treeId || '',
        size: this.paginationData.pageSize,
        dmId: this.spaceFilter.deptId
      }
      // 如果选择了空间分组，添加分组查询参数
      if (this.spaceFilter.spaceGroupId) {
        const selectedGroup = this.spaceGroupList.find((item) => item.dictionaryDetailsId === this.spaceFilter.spaceGroupId)
        if (selectedGroup) {
          data.roomSpaceType = this.spaceFilter.spaceGroupId
          data.roomSpaceTypeName = selectedGroup.dictionaryDetailsName
        }
      }
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == '200') {
          // 处理simName拆分
          this.spaceTableData = res.data.records.map((item) => {
            // 默认值
            const result = {
              ...item,
              hospital: '',
              area: '',
              building: '',
              floor: ''
            }
            if (item.simName) {
              const parts = item.simName.split('>')
              if (parts.length === 4) {
                // 如果有4个部分，分别赋值给医院、院区、建筑、楼层
                result.hospital = parts[0]
                result.area = parts[1]
                result.building = parts[2]
                result.floor = parts[3]
              } else if (parts.length === 3) {
                // 如果只有3个部分，赋值给院区、建筑、楼层
                result.area = parts[0]
                result.building = parts[1]
                result.floor = parts[2]
              } else if (parts.length === 2) {
                // 如果只有2个部分，赋值给建筑、楼层
                result.building = parts[0]
                result.floor = parts[1]
              } else if (parts.length === 1) {
                // 如果只有1个部分，赋值给楼层
                result.floor = parts[0]
              }
            }
            return result
          })
          this.paginationData.total = res.data.total
          this.spaceLoading = false
        }
      })
    },
    // 空间巡检点过滤
    spaceSearch() {
      this.paginationData.currentPage = 1
      this.getSpaceList(this.checkItem)
    },
    // 空间重置
    spaceReset() {
      this.paginationData.currentPage = 1
      this.spaceFilter.functionDictId = ''
      this.spaceFilter.localSpaceName = ''
      this.spaceFilter.localSpaceCode = ''
      this.spaceFilter.spaceGroupId = ''
      this.spaceFilter.deptId = ''
      this.getSpaceList(this.checkItem)
    },
    nodeClick(data, node) {
      if (this.activeName == 'space') {
        let prentIds = []
        let aimId = data.pid
        for (let i = 0; i < data.ssmType; i++) {
          const prent = this.allSpaceTreeData.find((i) => i.id == aimId)
          if (prent) {
            prentIds.unshift(prent.id)
            aimId = prent.pid
          } else {
            prentIds.push(data.id)
          }
        }
        this.checkItem = prentIds.join(',')
        this.getSpaceList(prentIds.join(','))
      } else if (this.activeName == 'equipment') {
        if (data.levelType == '1') {
          this.checkItem = data.id
          this.getDeviceList(data.id)
        } else {
          this.checkItem = data.parentId + ',' + data.id
          this.getDeviceList(data.parentId + ',' + data.id)
        }
      }
    },
    sizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      if (this.activeName == 'space') {
        this.getSpaceList(this.checkItem)
      } else if (this.activeName == 'equipment') {
        this.getDeviceList(this.checkItem)
      }
    },
    currentChange(val) {
      this.paginationData.currentPage = val
      if (this.activeName == 'space') {
        this.getSpaceList(this.checkItem)
      } else if (this.activeName == 'equipment') {
        this.getDeviceList(this.checkItem)
      }
    },
    // 设备分类列表
    getDeviceType() {
      this.$api.getProfessionalCategory().then((res) => {
        if (res.code == '200') {
          // this.treeData = transData(res.data, 'id', 'parentId', 'children')
          const idMap = res.data.reduce((acc, item) => {
            acc[item.id] = { ...item, children: [] }
            return acc
          }, {})
          const treeDataArr = []
          // 遍历数据，将子节点添加到对应的父节点的children数组中
          res.data.forEach((item) => {
            if (item.parentId === '-1') {
              treeDataArr.push(idMap[item.id])
            } else {
              const parent = idMap[item.parentId]
              if (parent) {
                parent.children.push(idMap[item.id])
              }
            }
          })
          // const unknown = {
          //   baseName: '未知',
          //   id: '-1',
          //   levelType: '1'
          // }
          // this.treeData.push(unknown)
          this.treeData = treeDataArr
          this.checkItem = this.treeData[0].id
          if (this.visible) {
            this.$nextTick(() => {
              this.$refs.tree.getTreeRef().setCurrentKey(this.checkItem)
            })
          }
          this.getDeviceList(this.checkItem)
        }
      })
    },
    // 设备列表
    getDeviceList(typeId) {
      this.spaceLoading = true
      let params = {
        assetName: this.deviceFilter,
        regionCode: this.regionCode.length > 0 ? this.regionCode[this.regionCode.length - 1] : '',
        pageSize: this.paginationData.pageSize,
        pageNo: this.paginationData.currentPage
      }
      // 如果选择了空间分组，添加分组查询参数
      if (this.spaceGroupFilter) {
        const selectedGroup = this.spaceGroupList.find((item) => item.dictionaryDetailsId === this.spaceGroupFilter)
        if (selectedGroup) {
          params.spaceGroupingId = this.spaceGroupFilter
          params.spaceGroupingName = selectedGroup.dictionaryDetailsName
        }
      }
      if (typeId && typeId.split(',').length >= 1) {
        const typeIdArr = typeId.split(',')
        if (typeIdArr.length === 1) {
          params.professionalCategoryCode = typeIdArr[0]
        } else if (typeIdArr.length > 1) {
          params.systemCategoryCode = typeIdArr[typeIdArr.length - 1]
        }
      }
      this.$api.selectDeviceGroupingList(params).then((res) => {
        if (res.code == '200') {
          // 处理位置信息拆分
          this.spaceTableData = res.data.list.map((item) => {
            // 默认值
            const result = {
              ...item,
              hospital: '',
              area: '',
              building: '',
              floor: '',
              room: ''
            }
            if (item.regionName) {
              // 替换HTML实体
              const regionName = item.regionName.replace(/&gt;/g, '>')
              const parts = regionName.split('>')
              if (parts.length === 5) {
                // 如果有5个部分，分别赋值给医院、院区、建筑、楼层、房间
                result.hospital = parts[0]
                result.area = parts[1]
                result.building = parts[2]
                result.floor = parts[3]
                result.room = parts[4]
              } else if (parts.length === 4) {
                // 如果有4个部分，分别赋值给医院、院区、建筑、楼层
                result.hospital = parts[0]
                result.area = parts[1]
                result.building = parts[2]
                result.floor = parts[3]
              } else if (parts.length === 3) {
                // 如果只有3个部分，赋值给院区、建筑、楼层
                result.area = parts[0]
                result.building = parts[1]
                result.floor = parts[2]
              } else if (parts.length === 2) {
                // 如果只有2个部分，赋值给建筑、楼层
                result.building = parts[0]
                result.floor = parts[1]
              } else if (parts.length === 1) {
                // 如果只有1个部分，赋值给楼层
                result.floor = parts[0]
              }
            }
            return result
          })
          this.paginationData.total = res.data.total
          this.spaceLoading = false
        }
      })
    },
    // 设备列表过滤
    deviceSearch() {
      this.paginationData.currentPage = 1
      this.getDeviceList(this.checkItem)
    },
    // 设备重置
    deviceReset() {
      this.paginationData.currentPage = 1
      this.deviceFilter = ''
      this.regionCode = []
      this.spaceGroupFilter = ''
      this.getDeviceList(this.checkItem)
    },
    // 设备详情
    detail(row) {
      this.deviceRowDetail = row
      this.dialogDateilVisible = true
    },
    // 选择巡检点
    selectRow(selection, row) {
      this.tags = selection
    },
    // 全选空间、设备巡检点
    selectAll(selection) {
      this.tags = selection
    },
    // 删除标签
    deleteTag(id) {
      this.tags.splice(
        this.tags.find((i) => i.id == id),
        1
      )
      const selectRow = this.spaceTableData.find((i) => i.id == id)
      const zdySelectRow = this.zdyTableData.find((i) => i.id == id)
      if (this.activeName == 'space') {
        this.$refs.spaceTable.toggleRowSelection(selectRow, false)
      } else if (this.activeName == 'equipment') {
        this.$refs.deviceTable.toggleRowSelection(selectRow, false)
      } else {
        this.$refs.zdyTable.toggleRowSelection(zdySelectRow, false)
      }
    },
    // 创建自定义巡检点
    addZdyInspectionPoint() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$api
            .addZdyInspectionPoint({
              taskPointTypeCode: 'zdy',
              taskPointTypeName: '自定义',
              taskPointName: this.customizeForm.name,
              taskPointCode: this.customizeForm.code,
              remarks: this.customizeForm.description,
              nfcCode: this.customizeForm.nfcCode,
              taskPointSourceFlag: this.systemType // 模块标识 1：设备设施，2：保养，3：巡检
            })
            .then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: '创建成功'
                })
                this.getZdyInspectionPoint()
                this.customizeForm = {
                  name: '',
                  code: '',
                  description: '',
                  nfcCode: ''
                }
              } else {
                this.$message.error(res.message)
              }
            })
        } else {
          this.$message({
            type: 'error',
            message: '请先完成必填项'
          })
          return false
        }
      })
    },
    // 获取自定义巡检点列表
    getZdyInspectionPoint() {
      this.$api
        .getZdyInspectionPointList({
          pageNo: this.zdyPaginationData.currentPage,
          pageSize: this.zdyPaginationData.pageSize,
          taskPointSourceFlag: this.systemType // 模块标识 1：设备设施，2：保养，3：巡检
        })
        .then((res) => {
          if (res.code == '200') {
            res.data.list.forEach((i) => (i.isEdit = false))
            this.zdyTableData = res.data.list
            this.zdyPaginationData.total = res.data.count
          }
        })
    },
    // 删除自定义巡检点
    deleteZdy(row) {
      this.$api
        .deleteZdyInspectionPoint({
          id: row.id,
          taskPointName: row.taskPointName,
          systemCode: this.systemType
        })
        .then((res) => {
          if (res.code == '200') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getZdyInspectionPoint()
          } else {
            this.$message({
              type: 'error',
              message: res.message || '删除失败'
            })
          }
        })
    },
    // 编辑自定义巡检点
    editZdy(row) {
      row.isEdit = true
    },
    // 保存编辑
    zdyUpdata(row) {
      console.log(row)
      if (row.taskPointName == '') {
        if (this.systemType == '2') {
          this.$message({
            type: 'error',
            message: '请输入保养点名称'
          })
        } else {
          this.$message({
            type: 'error',
            message: '请输入巡检点名称'
          })
        }
      } else if (row.taskPointCode == '') {
        if (this.systemType == '2') {
          this.$message({
            type: 'error',
            message: '请输入保养点编码'
          })
        } else {
          this.$message({
            type: 'error',
            message: '请输入巡检点编码'
          })
        }
      } else {
        this.$api
          .addZdyInspectionPoint({
            taskPointTypeCode: 'zdy',
            taskPointTypeName: '自定义',
            id: row.id,
            taskPointName: row.taskPointName,
            taskPointCode: row.taskPointCode,
            remarks: row.remarks,
            nfcCode: row.nfcCode,
            taskPointSourceFlag: this.systemType // 模块标识 1：设备设施，2：保养，3：巡检
          })
          .then((res) => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: '保存成功'
              })
              this.getZdyInspectionPoint()
              row.isEdit = false
            }
          })
      }
    },
    zdySizeChange(val) {
      this.zdyPaginationData.pageSize = val
      this.zdyPaginationData.currentPage = 1
      this.getZdyInspectionPoint()
    },
    zdyCurrentChange(val) {
      this.zdyPaginationData.currentPage = val
      this.getZdyInspectionPoint()
    },
    // 确认选择
    checkConfirm() {
      if (this.activeName == 'customize') {
        if (this.zdyTableData.some((i) => i.isEdit)) {
          if (this.systemType == '2') {
            this.$message({
              type: 'error',
              message: '有未保存的保养点，请先保存'
            })
          } else {
            this.$message({
              type: 'error',
              message: '有未保存的巡检点，请先保存'
            })
          }
        } else {
          this.tags.forEach((i) => {
            i.projectId = ''
            i.projectName = ''
          })
          this.$emit('close')
          this.$emit('confirm', this.tags)
        }
      } else {
        this.tags.forEach((i) => {
          i.projectId = ''
          i.projectName = ''
        })
        this.$emit('close')
        this.$emit('confirm', this.tags)
      }
    },
    getRegionList() {
      this.$api.spaceTree().then((res) => {
        if (res.code == '200') {
          this.regionCodeList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 获取空间分组列表
    getSpaceGroupList() {
      this.$api
        .getSpaceGroupList({
          current: 1,
          size: 999,
          dictionaryCategoryId: 'space_group',
          level: 1
        })
        .then((res) => {
          if (res.code == '200') {
            this.spaceGroupList = res.data.list || []
          }
        })
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getDeptList().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.dialogContent {
  height: 100%;
  .topMenu {
    width: 100%;
    position: absolute;
    top: 55px;
    left: 0;
    :deep(.el-tabs) {
      width: 100%;
      .el-tabs__header {
        width: 100%;
        background-color: #fff;
        .el-tabs__item {
          padding: 0 20px;
        }
      }
    }
  }
  .pointListWrap {
    height: 100%;
    .spaceContent {
      display: flex;
      justify-content: space-between;
      .leftTree {
        background-color: #fff;
        padding: 0 16px;
        width: 250px;
        height: 425px;
        .filter-tree {
          height: 343px;
          overflow: auto;
        }
        ::v-deep .custom-tree {
          height: calc(100% - 82px);
          overflow: auto;
        }
        // :deep(.el-tree) {
        // }
      }
      .rightTable {
        background-color: #fff;
        width: calc(100% - 268px);
        height: 425px;
        padding: 0 16px;
        .topFilter {
          width: 100%;
          padding: 16px 0 0;
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          // justify-content: space-between;
          :deep(.el-input) {
            width: 200px;
          }
          .spaceFilter {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            width: 100%;
            :deep(.el-input) {
              width: 200px;
            }
          }
          .deviceFilter {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            width: 100%;
            :deep(.el-input) {
              // width: 260px;
              margin-right: 15px;
            }
          }
        }
        .selected {
          height: 40px;
          margin: 10px 0;
          display: flex;
          align-items: center;
          > span {
            display: inline-block;
            margin-right: 10px;
            width: 42px;
          }
          .tagsWrap {
            width: calc(100% - 52px);
            height: 100%;
            display: flex;
            align-items: center;
            overflow-x: auto;
            overflow-y: hidden;
            > span {
              margin-right: 10px;
            }
          }
          .tagsWrap::-webkit-scrollbar {
            height: 10px;
          }
        }
        .table {
          :deep(.el-table) {
            .el-table__cell {
              padding: 10px 0;
            }
          }
        }
      }
    }
    .customizeContent {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .topBaseInfo {
        height: 200px;
        padding: 0 16px;
        width: 100%;
        background-color: #fff;
      }
      .customizePointList {
        margin-top: 24px;
        padding: 0 16px;
        height: calc(100% - 224px);
        background-color: #fff;
        .zdyTopTitle {
          display: flex;
          width: 100%;
          > .toptip {
            width: 130px !important;
            margin-right: 10px;
          }
          .selected {
            width: calc(100% - 140px);
            height: 40px;
            margin: 5px 0;
            display: flex;
            align-items: center;
            > span {
              display: inline-block;
              margin-right: 10px;
              width: 42px;
            }
            .tagsWrap {
              width: calc(100% - 52px);
              height: 100%;
              display: flex;
              align-items: center;
              overflow-x: auto;
              overflow-y: hidden;
              > span {
                margin-right: 10px;
              }
            }
            .tagsWrap::-webkit-scrollbar {
              height: 10px;
            }
          }
        }
        .table {
          :deep(.el-table) {
            .el-table__cell {
              padding: 10px 0;
            }
          }
        }
      }
    }
  }
}
.inspectionPointDateil {
  :deep(.el-dialog) {
    .el-dialog__header {
      border-bottom: 1px solid #dcdfe6;
    }
    .el-dialog__body {
      background-color: #f6f5fa;
      padding: 24px;
    }
    .el-dialog__footer {
      padding: 10px 20px;
    }
  }
  .detailDialog {
    background-color: #fff;
    padding: 0 16px;
    .cosRow {
      display: flex;
      align-items: center;
      padding: 12px 0;
      .titleWrap {
        color: #414653;
        width: 100px;
        text-align: right;
      }
      .contenWrap {
        color: #121f3e;
        margin-left: 14px;
      }
    }
  }
  .typeInfo {
    margin-top: 24px;
  }
}
.spaceDialog {
  > :deep(.el-dialog) {
    height: 620px;
  }
}
.customizeDialog {
  > :deep(.el-dialog) {
    height: 800px !important;
  }
}
:deep(.el-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #dcdfe6;
  }
  .el-dialog__body {
    background-color: #f6f5fa;
    padding: 64px 24px 24px;
    height: calc(100% - 107px);
  }
  .el-dialog__footer {
    padding: 10px 20px;
  }
}
.el-cascader {
  line-height: 30px;
}
</style>
