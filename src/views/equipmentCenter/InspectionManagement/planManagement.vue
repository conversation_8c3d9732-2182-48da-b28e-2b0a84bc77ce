<template>
  <div style="height: 100%">
    <div class="special_box">
      <div class="left">
        <div class="leftCon">
          <div class="toptip">
            <span class="green_line"></span>
            计划管理
          </div>
          <template>
            <el-tabs v-if="systemType != '3'" v-model="activeName" @tab-click="handleClick">
              <el-tab-pane v-for="(item, index) in tabsOption" :key="index" :label="item.label" :name="item.name"> </el-tab-pane>
            </el-tabs>
            <div class="tabsBox">
              <el-tree
                ref="tree"
                v-loading="treeLoading"
                :data="treeData"
                :props="treeProps"
                node-key="id"
                :filter-node-method="filterNode"
                :highlight-current="true"
                :check-strictly="true"
                :default-expanded-keys="expanded"
                @node-click="nodeClick"
              >
                <span slot-scope="{ node }" class="custom-tree-node" style="width: 100%">
                  <el-tooltip v-if="node.label.length > 17" class="item" effect="dark" :content="node.label" placement="top-start">
                    <span class="treeLabel">{{ node.label }}</span>
                  </el-tooltip>
                  <span v-else class="treeLabel">{{ node.label }}</span>
                </span>
              </el-tree>
            </div>
          </template>
        </div>
      </div>
      <div class="content_box">
        <div class="top_content">
          <div style="margin-bottom: 20px">
            <el-input
              v-model="filterData.planName"
              style="width: 200px; margin-right: 20px"
              placeholder="计划名称"
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
              @keyup.enter.native="searchByCondition"
            ></el-input>
            <el-select v-model="filterData.cycleType" placeholder="请选择周期类型">
              <el-option v-for="item in cycleTypeList" :key="item.cycleType" :label="item.label" :value="item.cycleType"> </el-option>
            </el-select>
            <el-input
              v-model="filterData.dept"
              style="width: 180px; margin: 0 20px"
              :placeholder="systemType == '2' ? '保养部门' : '巡检部门'"
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
              @keyup.enter.native="searchByCondition"
            ></el-input>
            <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="resetCondition">重置</el-button>
            <el-button type="primary" style="font-size: 14px" @click="searchByCondition">查询</el-button>
          </div>
          <div>
            <el-button type="primary" icon="el-icon-plus" style="font-size: 14px" @click="addFn('add')">新增</el-button>
            <el-button type="primary" :disabled="tableClickArry.length == 0" style="width: 90px; padding: 8px; font-size: 14px" :loading="isdownload" @click="downLoadQrCode">{{
              systemType == '2' ? '保养码下载' : '巡检码下载'
            }}</el-button>
          </div>
        </div>
        <div class="table_list" style="text-align: right; height: 100%">
          <el-table
            ref="materialTable"
            v-loading="tableLoading"
            :data="tableData"
            height="calc(100% - 10px)"
            border
            :header-cell-style="{ background: '#F6F5FA' }"
            style="width: 100%"
            :cell-style="{ padding: '8px 0 8px 0' }"
            stripe
            highlight-current-row
            :empty-text="emptyText"
            row-key="id"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="60" align="center"></el-table-column>
            <el-table-column label="序号" type="index" width="60">
              <template slot-scope="scope">
                <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="计划名称">
              <template slot-scope="scope">
                <el-link type="primary" @click="planDetail(scope.row, 'detail')">{{ scope.row.planName }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="planTypeName" label="计划类型"></el-table-column>
            <el-table-column label="周期类型" width="95">
              <template slot-scope="scope">
                <span>{{ cycleTypeFn(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="开始日期">
              <template slot-scope="scope">
                <span>{{
                  scope.row.cycleType == '8' || scope.row.cycleType == '5'
                    ? moment(scope.row.startDate).format('YYYY-MM-DD')
                    : moment(scope.row.createStartTime).format('YYYY-MM-DD')
                }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="结束日期">
              <template slot-scope="scope">
                <span>
                  {{
                    scope.row.cycleType == '5' || scope.row.cycleType == '8'
                      ? moment(scope.row.startDate).add(scope.row.finalTime, 'd').format('YYYY-MM-DD')
                      : moment(scope.row.createEndTime).format('YYYY-MM-DD')
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="distributionTeamName" show-overflow-tooltip :label="systemType == '2' ? '保养部门' : '巡检部门'" width="115"> </el-table-column>
            <el-table-column label="状态" width="115">
              <template slot-scope="scope">
                <div>
                  <span v-if="scope.row.useState == '1'" class="disable">
                    <span></span>
                    已禁用
                  </span>
                  <span v-if="scope.row.useState == '0'" class="enable">
                    <span></span>
                    已启用
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="完成率" width="115">
              <template slot-scope="scope">
                <span>{{ scope.row.percentageComplete + '%' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="185">
              <template slot-scope="scope">
                <el-link @click="copyPlan(scope.row)">复制</el-link>
                <el-link :type="scope.row.useState == '1' ? 'danger' : 'info'" :disabled="scope.row.useState == '0'" @click="operation(scope.row, 'delete')"> 删除 </el-link>
                <el-link :type="scope.row.useState == '1' ? 'primary' : 'info'" :disabled="scope.row.useState == '0'" @click="operation(scope.row, 'edit')">编辑</el-link>
                <el-link type="primary">
                  <el-popconfirm title="是否修改此计划的状态？" @confirm="operation(scope.row, 'status')">
                    <span v-if="scope.row.useState == '1'" slot="reference">启用</span>
                    <span v-else slot="reference">禁用</span>
                  </el-popconfirm>
                </el-link>
                <el-link :type="scope.row.useState == '0' ? 'primary' : 'info'" :disabled="scope.row.useState == '1'" @click="progress(scope.row)">进度</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="" style="padding-top: 10px; text-align: right">
          <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
import qs from 'qs'
import { transData } from '@/util'
import moment from 'moment'
export default {
  name: 'planManagement',
  components: {},
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addPlans', 'progressDetail'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      moment,
      isdownload: false,
      treeLoading: false,
      activeName: '0',
      systemType: '',
      tabsOption: [
        {
          name: '0',
          label: '模板分类'
        },
        {
          name: '1',
          label: '设备类别'
        }
      ],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      // tree选中项
      checkItem: {},
      // 周期类型
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        },
        {
          cycleType: 7,
          label: '自定义'
        }
      ],
      tableData: [],
      // 列表过滤条件
      filterData: {
        planName: '',
        cycleType: '',
        dept: ''
      },
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      listRole: '',
      userIds: '',
      activeType: '',
      tableClickArry: [],
      allTreeData: [],
      treeData: [],
      treeProps: {
        children: 'children',
        label: (data, node) => {
          if (this.activeName == '0') {
            return data.planTypeName + '(' + data.sum + ')'
          } else {
            return data.baseName
          }
        },
        isLeaf: 'leaf'
      },
      useState: '',
      downLoadId: '',
      staging: null,
      expanded: [],
      treeDataArrAll: []
    }
  },
  watch: {
    activeName(e, l) {
      this.treeData = []
      this.tableData = []
      if (e == '0') {
        this.getTemplateClassification()
      }
      if (e == '1') {
        this.getDeviceType()
      }
    }
  },
  created() {
    this.initEvent()
  },
  activated() {
    this.initEvent()
  },
  mounted() {},
  methods: {
    initEvent() {
      if (this.$route.path.indexOf('/InspectionManagement') != -1) {
        this.systemType = '1'
      } else if (this.$route.path.indexOf('/MaintenanceManagement') != -1) {
        this.systemType = '2'
      } else if (this.$route.path.indexOf('/comInspectionManagement') != -1) {
        this.systemType = '3'
      }
      this.$nextTick(() => {
        if (this.$route.query.activeName) {
          this.activeName = this.$route.query.activeName
          if (this.activeName == '0') {
            this.getTemplateClassification()
          } else {
            this.getDeviceType()
          }
        } else {
          this.getTemplateClassification()
        }
      })
    },
    handleClick(tab) {
      this.$router.push({ query: { activeName: this.activeName } })
    },
    // 获取模板分类
    getTemplateClassification() {
      this.treeLoading = true
      this.$api.getTemplateType({
        systemIdentificationClassification: this.systemType,
        planOrTask: '1' // "1"：查询计划sum "0" ：查询任务sum
      }).then(res => {
        if (res.code == '200') {
          if (this.activeName == '0') {
            if (res.data.length > 0) {
              this.treeData = res.data.map(i => {
                i.id = i.planTypeId
                return i
              })
              this.treeDataArrAll = res.data
              this.treeData = transData(res.data, 'planTypeId', 'parentId', 'children')
              if (this.$route.query.typeId) {
                this.checkItem.id = this.$route.query.typeId
                this.checkItem.parentId = this.treeDataArrAll.find(i => i.id == this.checkItem.id).parentId
                this.checkItem.levelType = this.treeDataArrAll.find(i => i.id == this.checkItem.id).levelType
              } else {
                this.checkItem = this.treeData[0]
              }
              this.$nextTick(() => {
                this.$refs.tree.setCurrentKey(this.checkItem.id)
              })
              this.getPlanList(this.checkItem.id)
            }
          }
        } else {
          this.$message.error(res.message || '获取失败！')
        }
        this.treeLoading = false
      })
    },
    // 设备分类列表
    getDeviceType() {
      this.treeLoading = true
      this.$api.getDeviceType({
        levelType: 3
      }).then((res) => {
        if (res.code == '200') {
          if (this.activeName == '1') {
            if (res.data.length > 0) {
              this.allTreeData = res.data
              this.treeData = transData(res.data, 'id', 'parentId', 'children')
              if (this.$route.query.typeId) {
                this.checkItem.id = this.$route.query.typeId
                this.checkItem.parentId = this.allTreeData.find(i => i.id == this.checkItem.id).parentId
                this.checkItem.levelType = this.allTreeData.find(i => i.id == this.checkItem.id).levelType
                this.expanded = []
                this.expendTreeNode(res.data, this.checkItem.id)
              } else {
                this.checkItem = this.treeData[0]
              }
              this.$nextTick(() => {
                this.$refs.tree.setCurrentKey(this.checkItem.id)
              })
              this.getPlanList(this.checkItem.id)
            }
          }
        } else {
          this.$message.error(res.message || '获取失败！')
        }
        this.treeLoading = false
      })
    },
    // 查找默认展开节点
    expendTreeNode(arr, id) {
      arr.forEach(i => {
        if (i.parentId != '-1') {
          if (i.id == id) {
            this.expanded.unshift(i.parentId)
            this.expendTreeNode(arr, i.parentId)
          }
        } else {
          return
        }
      })
    },
    // 获取计划列表
    getPlanList(typeId) {
      const params = {
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        planName: this.filterData.planName,
        cycleType: this.filterData.cycleType,
        distributionTeamName: this.filterData.dept,
        planTypeId: typeId,
        systemCode: this.systemType, // 1:设备 2: 保养 3：巡检
        deleteFlag: 0 // 0:为删除 1:已删除
      }
      this.tableLoading = true
      this.$api.getPlansList(params).then(res => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.count
        }
        this.tableLoading = false
      })
    },
    filterNode(value, data) {
      if (this.activeName == '0') {
        if (!value) return true
        return data.planTypeName.indexOf(value) !== -1
      } else if (this.activeName == '1') {
        if (!value) return true
        return data.baseName.indexOf(value) !== -1
      }
    },
    nodeClick(data, node, event) {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.checkItem = data
      this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
      this.$router.push({ query: {
        activeName: this.activeName,
        typeId: this.checkItem.planTypeId || this.checkItem.id
      }})
    },
    findTreeId(data) {
      const prentIds = []
      prentIds.unshift(data.id)
      if (data.levelType == '3') {
        this.allTreeData.find(i => {
          if (i.id == data.parentId) {
            prentIds.unshift(i.id)
            this.allTreeData.find(j => {
              if (j.id == i.parentId) {
                prentIds.unshift(j.id)
              }
            })
          }
        })
      } else if (data.levelType == '2') {
        this.allTreeData.find(i => {
          if (i.id == data.parentId) {
            prentIds.unshift(i.id)
          }
        })
      }
      return prentIds
    },
    // 复制
    copyPlan(row) {
      const deviceIds = this.findTreeId(this.checkItem)
      const deviceIdsSort = this.findTreeIdSort(this.checkItem)
      sessionStorage.setItem('row', JSON.stringify(row))
      this.$router.push({
        path: 'planManagement/addPlans',
        query: {
          activeType: 'copy',
          id: this.activeName == '0' ? deviceIdsSort : deviceIds,
          type: this.activeName,
          row
        }
      })
    },
    // 新增
    addFn(type) {
      const deviceIds = this.findTreeId(this.checkItem)
      const deviceIdsSort = this.findTreeIdSort(this.checkItem)
      this.$router.push({
        path: 'planManagement/addPlans',
        query: {
          activeType: type,
          id: this.activeName == '0' ? deviceIdsSort : deviceIds,
          type: this.activeName
        }
      })
    },
    findTreeIdSort(data) {
      console.log(this.treeDataArrAll)
      const parentIds = []
      let currentId = data.id
      while (currentId) {
        const item = this.treeDataArrAll.find(item => item.planTypeId == currentId)
        if (item) {
          parentIds.unshift(item.planTypeId)
          currentId = item.parentId
        } else {
          break
        }
      }
      return parentIds
    },
    // 进度
    progress(row) {
      this.$router.push({
        path: 'planManagement/progressDetail',
        query: {
          id: row.id
        }
      })
    },
    handleSelectionChange(val) {
      this.tableClickArry = val
    },
    // 巡检码下载
    downLoadQrCode() {
      this.downLoadId =
        this.tableClickArry &&
        this.tableClickArry.length &&
        this.tableClickArry.map((item, index) => {
          return item.id
        })
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        planIds: this.downLoadId.join(',') || '',
        unitCode: userInfo.unitCode || 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode || 'BJSJTYY'
      }
      // const qs = require('qs')
      this.isdownload = true
      fetch(`${__PATH.VUE_ICIS_API}/taskPoint/exportQrCode`, {
        method: 'POST',
        responseType: 'blob',
        body: qs.stringify(params),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }
      )
        .then((res) => {
          res.blob().then((blob) => {
            this.saveBlobAs(blob)
            this.isdownload = false
          })
        })
        .catch((err) => {
          console.log(err)
          this.isdownload = false
        })
    },
    // 保存blob的方法
    saveBlobAs(blob) {
      let _self = this
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob)
      } else {
        const anchor = document.createElement('a')
        const body = document.querySelector('body')
        anchor.href = window.URL.createObjectURL(blob)
        if (this.systemType == '2') {
          anchor.download = '保养码.zip'
        } else {
          anchor.download = '巡检码.zip'
        }
        anchor.style.display = 'none'
        body.appendChild(anchor)
        anchor.click()
        body.removeChild(anchor)
        window.URL.revokeObjectURL(anchor.href)
        this.exportLoading = false
      }
    },
    // 条件查询
    searchByCondition() {
      this.paginationData.currentPage = 1
      this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
    },
    // 重置查询条件
    resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.filterData = {
        planName: '',
        cycleType: '',
        dept: ''
      }
      this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeList.filter((i) => i.cycleType == row.cycleType)
      console.log('item', item)
      return item[0].label
    },
    // 列表操作
    operation(row, type) {
      // 编辑
      if (type == 'edit') {
        const deviceIds = this.findTreeId(this.checkItem)
        const deviceIdsSort = this.findTreeIdSort(this.checkItem)
        sessionStorage.setItem('row', JSON.stringify(row))
        this.$router.push({
          path: 'planManagement/addPlans',
          query: {
            activeType: type,
            id: this.activeName == '0' ?  deviceIdsSort : deviceIds,
            type: this.activeName,
            row
          }
        })
      } else if (type == 'status') {
        const status =  row.useState == '1' ? 'enable' : 'disable'
        this.$api.updateMaintainPlanState({
          id: row.id,
          useState: status == 'enable' ? 0 : 1 // 0:启用，1：禁用
        }).then(res => {
          if (res.code == '200') {
            this.$message({
              type: 'success',
              message: `${status == 'enable' ? '启用' : '禁用'}成功`
            })
            this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
          } else {
            this.$message({
              type: 'error',
              message: res.message || '修改计划状态失败'
            })
          }
        })
      } else if (type == 'delete') {
        this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          this.$api.deletePlan({id: row.id}, {'operation-type': 3, 'operation-name': row.planName, 'operation-id': row.id}).then(res => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              if (this.activeName == '0') {
                this.getTemplateClassification()
              } else {
                this.getDeviceType()
              }
              this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
            } else {
              this.$message({
                type: 'error',
                message: res.message || '删除失败'
              })
            }
          })
        }).catch(() => {})
      }
    },
    // 计划详情
    planDetail(row, type) {
      this.tableLoading = true
      sessionStorage.setItem('row', JSON.stringify(row))
      this.$router.push({
        path: 'planManagement/addPlans',
        query: {
          activeType: type,
          id: this.checkItem.planTypeId || this.checkItem.id,
          type: this.activeName
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.tabsItem {
  padding: 8px 0;
  width: 200px;
  margin: 0 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #5b5b5b;
  cursor: pointer;
  .item {
    width: 200px !important;
  }
  > span {
    width: 200px !important;
    margin: 0 auto !important;
  }
}
.tabsItem:hover {
  color: #5188fc;
}
:deep(.el-tabs--left) {
  width: 200px;
  margin: 20px auto 0;
}
:deep(.el-table__row) {
  :last-child > .cell {
    display: flex;
    justify-content: space-between;
  }
}
.special_box {
  height: 100%;
  display: flex;
  .left {
    width: 246px;
    min-width: 14%;
    height: calc(100% - 30px);
    border-radius: 10px;
    background-color: #fff;
    margin: 15px;
    .leftCon {
      padding: 10px;
      height: 100%;
      // .toptip {
      //   box-sizing: border-box;
      //   padding-left: 26px;
      //   height: 50px;
      //   width: 100%;
      //   line-height: 50px;
      //   text-align: left;
      //   border-bottom: none;
      //   font-weight: 600;
      // }
      .tabsBox {
        text-align: center;
        margin: 10px 0;
        height: calc(100% - 100px);
        overflow: auto;
        ::v-deep .el-tree-node__content {
          width: 100%;
          text-align: left;
          .custom-tree-node {
            width: calc(100% - 24px) !important;
            .treeLabel {
              display: inline-block;
              width: 100%;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
          }
        }
      }
    }
  }
  .content_box {
    width: calc(100% - 276px);
    margin: 15px 0;
    border-radius: 10px;
    padding: 20px 25px 25px;
    background: #fff;
    display: flex;
    flex-direction: column;
    .top_content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 25px;
    }
    .table_list {
      .disable {
        color: #414653;
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #ccced3;
        }
      }
      .enable {
        color: #08cb83;
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #08cb83;
        }
      }
    }
  }
}
:deep(.el-tabs__nav) {
  width: 100%;
  .el-tabs__item {
    padding: 0 10px;
    width: 50%;
    text-align: center;
  }
}
</style>
