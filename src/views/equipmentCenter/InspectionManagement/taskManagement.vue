<template>
  <div v-loading="exportLoading" style="height: 100%">
    <div class="special_box">
      <div class="left">
        <div class="leftCon">
          <div class="toptip">
            <span class="green_line"></span>
            任务管理
          </div>
          <template>
            <el-tabs v-if="systemType != '3'" v-model="activeName" @tab-click="handleClick">
              <el-tab-pane v-for="(item, index) in tabsOption" :key="index" :label="item.label" :name="item.name"> </el-tab-pane>
            </el-tabs>
            <div class="tabsBox">
              <el-tree
                ref="tree"
                v-loading="treeLoading"
                :class="activeName == '0' ? 'templateType' : ''"
                :data="treeData"
                :props="treeProps"
                node-key="id"
                :filter-node-method="filterNode"
                :highlight-current="true"
                :default-expanded-keys="expanded"
                @node-click="nodeClick"
              >
                <span slot-scope="{ node }" class="custom-tree-node" style="width: 100%">
                  <el-tooltip v-if="node.label.length > 17" class="item" effect="dark" :content="node.label" placement="top-start">
                    <span class="treeLabel">{{ node.label }}</span>
                  </el-tooltip>
                  <span v-else class="treeLabel">{{ node.label }}</span>
                </span>
              </el-tree>
            </div>
          </template>
        </div>
      </div>
      <div class="content_box">
        <div class="top_content">
          <el-input
            v-model="filterData.taskName"
            style="width: 200px; margin-right: 20px"
            placeholder="任务名称"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
            @keyup.enter.native="searchByCondition"
          ></el-input>
          <el-input
            v-model="filterData.dept"
            style="width: 180px; margin-right: 20px"
            :placeholder="systemType == '2' ? '保养部门' : '巡检部门'"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
            @keyup.enter.native="searchByCondition"
          ></el-input>
          <el-input
            v-model="filterData.personName"
            style="width: 200px; margin-right: 20px"
            :placeholder="systemType == '2' ? '保养人员' : '巡检人员'"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
            @keyup.enter.native="searchByCondition"
          ></el-input>
          <el-select v-model="filterData.acceptance" placeholder="验收状态">
            <el-option v-for="item in acceptanceStatus" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
          <el-select v-model="filterData.status" placeholder="任务状态" style="margin: 0 20px">
            <el-option v-for="item in statusOption" :key="item.id" :label="item.label" :value="item.id"> </el-option>
          </el-select>
          <el-select v-model="filterData.accomplishType" placeholder="超时状态" style="margin: 0 20px">
            <el-option v-for="item in statusOvertimeTask" :key="item.id" :label="item.label" :value="item.id"> </el-option>
          </el-select>
          <el-date-picker
            v-model="filterData.date"
            type="daterange"
            start-placeholder="开始日期"
            range-separator="至"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
            @change="handleDateChange"
          >
          </el-date-picker>
        </div>
        <div class="top_content">
          <div>
            <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="resetCondition">重置</el-button>
            <el-button type="primary" style="font-size: 14px" @click="searchByCondition">查询</el-button>
            <el-button :disabled="selectionList.length == 0" type="primary" style="font-size: 14px" @click="acceptance()">批量验收</el-button>
            <el-button :disabled="selectionList.length == 0" type="primary" style="font-size: 14px" @click="batchDelete">批量删除</el-button>
          </div>
          <el-popover placement="left" width="180" trigger="hover">
            <el-button type="primary" style="font-size: 14px" icon="el-icon-upload2" @click="exportExcel">导出任务数据</el-button>
            <el-button type="primary" style="font-size: 14px; margin: 5px 0 0 0" icon="el-icon-upload2" @click="exportDetail">导出任务明细</el-button>
            <el-button slot="reference" type="primary">导出</el-button>
          </el-popover>
        </div>
        <div class="table_list" style="text-align: right; height: 100%">
          <el-table
            ref="materialTable"
            v-loading="tableLoading"
            :data="tableData"
            height="calc(100% - 10px)"
            border
            :header-cell-style="{ background: '#F6F5FA' }"
            style="width: 100%"
            :cell-style="{ padding: '8px 0 8px 0' }"
            stripe
            highlight-current-row
            :empty-text="emptyText"
            row-key="id"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column label="序号" type="index" width="50">
              <template slot-scope="scope">
                <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="任务名称">
              <template slot-scope="scope">
                <el-link type="primary" @click="operation(scope.row, 'detail')">{{ scope.row.taskName }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="planName" show-overflow-tooltip label="所属计划"></el-table-column>
            <el-table-column label="周期类型" width="80" align="center">
              <template slot-scope="scope">
                <span>{{ cycleTypeFn(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="systemType == '2' ? '应保养日期' : '应巡日期'">
              <template slot-scope="scope">
                <span>{{ moment(scope.row.taskStartTime).format('YYYY-MM-DD') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="distributionTeamName" show-overflow-tooltip :label="systemType == '2' ? '保养部门' : '巡检部门'"></el-table-column>
            <el-table-column prop="planPersonName" show-overflow-tooltip :label="systemType == '2' ? '保养人员' : '巡检人员'"></el-table-column>
            <el-table-column prop="totalCount" :label="systemType == '2' ? '应保养点数' : '应巡点数'" width="80" align="center"></el-table-column>
            <el-table-column prop="hasCount" :label="systemType == '2' ? '实保养点数' : '实巡点数'" width="80" align="center"></el-table-column>
            <el-table-column label="完成状态" width="90" align="center">
              <template slot-scope="scope">
                <div>
                  <span v-if="scope.row.taskStatus == '1'" class="disable">
                    <span></span>
                    未完成
                  </span>
                  <span v-if="scope.row.taskStatus == '2'" class="enable">
                    <span></span>
                    已完成
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="超时状态" width="90" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.overtimeTask == '已超时'" style="color: red">{{ scope.row.overtimeTask }}</span>
                <span v-if="scope.row.overtimeTask == '未超时'">{{ scope.row.overtimeTask }}</span>
              </template>
            </el-table-column>
            <el-table-column label="验收状态" width="90" align="center">
              <template slot-scope="scope">
                <el-popover v-if="scope.row.inspectionReportId == 1 || scope.row.inspectionReportId == 2" placement="left" width="240" trigger="hover">
                  <div class="recordContent">
                    <div class="recordWrap">
                      <span class="recordTitle">验收人：</span>
                      <span>{{ scope.row.auftragnehmerName }}</span>
                    </div>
                    <div class="recordWrap">
                      <span class="recordTitle">验收结论：</span>
                      <span>{{ scope.row.inspectionReportName }}</span>
                    </div>
                    <div class="recordWrap">
                      <span class="recordTitle">验收时间：</span>
                      <span>{{ scope.row.auftragnehmerTime }}</span>
                    </div>
                    <div class="recordWrap">
                      <span class="recordTitle">验收意见：</span>
                      <el-tooltip v-if="scope.row.acceptanceOpinion.length > 10" effect="dark" :content="scope.row.acceptanceOpinion" placement="top-start">
                        <span class="textRecord">{{ scope.row.acceptanceOpinion }}</span>
                      </el-tooltip>
                      <span v-else>{{ scope.row.acceptanceOpinion }}</span>
                    </div>
                    <div class="recordWrap">
                      <span class="recordTitle">附件：</span>
                      <template v-if="scope.row.accessoryArr">
                        <el-tooltip v-if="scope.row.accessoryArr.length > 10" effect="dark" :content="scope.row.accessoryArr" placement="top-start">
                          <el-link class="textRecord" type="primary" :underline="false" @click="dwonLoadFile(scope.row.accessoryArr, scope.row.accessoryUrl)">
                            {{ scope.row.accessoryArr }}
                          </el-link>
                        </el-tooltip>
                        <el-link v-else class="textRecord" type="primary" :underline="false" @click="dwonLoadFile(scope.row.accessoryArr, scope.row.accessoryUrl)">
                          {{ scope.row.accessoryArr }}
                        </el-link>
                      </template>
                      <template v-else>
                        <span>暂无</span>
                      </template>
                    </div>
                    <div class="recordWrap">
                      <span class="recordTitle">签字：</span>
                      <el-image style="width: 100px; height: 60px" :src="scope.row.signatureUrl" :preview-src-list="[$tools.imgUrlTranslation(scope.row.signatureUrl)]"> </el-image>
                    </div>
                  </div>
                  <span slot="reference" class="acceptanceText" :class="'acceptanceColor' + scope.row.inspectionReportId">
                    {{ !scope.row.inspectionReportId || scope.row.inspectionReportId == 0 ? '未验收' : scope.row.inspectionReportId == 1 ? '验收通过' : '验收驳回' }}
                  </span>
                </el-popover>
                <span v-else :class="'acceptanceColor' + scope.row.inspectionReportId">
                  {{ !scope.row.inspectionReportId || scope.row.inspectionReportId == 0 ? '未验收' : scope.row.inspectionReportId == 1 ? '验收通过' : '验收驳回' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-link
                  :type="scope.row.taskStatus == '2' || moment(scope.row.taskEndTime) < moment() ? 'info' : 'danger'"
                  :disabled="scope.row.taskStatus == '2' || moment(scope.row.taskEndTime) < moment()"
                  @click="operation(scope.row, 'delete')"
                >
                  删除
                </el-link>
                <el-link
                  :type="(scope.row.taskStatus == '1' && moment(scope.row.taskEndTime) < moment()) || scope.row.taskStatus == '2' ? 'info' : 'primary'"
                  :disabled="scope.row.taskStatus == '2' || moment(scope.row.taskEndTime) < moment()"
                  @click="operation(scope.row, 'edit')"
                >
                  编辑
                </el-link>
<!--                需求 3943 需要隐藏状态修改-->
<!--                <el-link v-if="scope.row.taskStatus == '2' || moment(scope.row.taskEndTime) > moment()" disabled type="info"> 状态修改 </el-link>-->
<!--                <el-link v-else type="primary" @click="operation(scope.row, 'status')">状态修改</el-link>-->
                <el-link type="primary" :disabled="scope.row.taskStatus != 2 || scope.row.inspectionReportId === '1'" @click="operation(scope.row, 'check')">验收</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="" style="padding-top: 10px; text-align: right">
          <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
    <div v-if="statusVisible">
      <el-dialog class="changeStatusDialog" title="任务状态修改" :visible.sync="statusVisible" width="30%" :before-close="handleClose">
        <changeStatus ref="changeStatusDialog" :detail="checkDetail" @updata="updataList"></changeStatus>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="statusVisible = false">取消</el-button>
          <el-button v-loading="statusLoding" type="primary" @click="saveStatus">保存</el-button>
        </span>
      </el-dialog>
    </div>
    <div v-if="showAcceptance">
      <el-dialog class="acceptanceDialog" title="任务验收" :close-on-click-modal="false" :visible.sync="showAcceptance" width="40%" :before-close="handleCloseAcceptance">
        <acceptanceContent
          ref="acceptanceDialog"
          :listData="acceptanceList"
          :systemType="systemType"
          :cycleTypeList="cycleTypeList"
          @close="handleCloseAcceptance"
          @updata="getTemplateClassification"
        >
        </acceptanceContent>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { transData } from '@/util'
import moment from 'moment'
import axios from 'axios'
import changeStatus from './components/changeStatus.vue'
import acceptanceContent from './components/acceptanceContent.vue'
export default {
  name: 'taskManagement',
  components: {
    changeStatus,
    acceptanceContent
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      if (from.query.activeName) {
        vm.activeName = from.query.activeName
      }
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['taskDetail', 'taskPointEdit'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      moment,
      isdownload: false,
      statusVisible: false,
      statusLoding: false,
      treeLoading: false,
      systemType: '',
      activeName: '0',
      tabsOption: [
        {
          name: '0',
          label: '模板分类'
        },
        {
          name: '1',
          label: '设备类别'
        }
      ],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      // tree选中项
      checkItem: {},
      // 周期类型
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        },
        {
          cycleType: 7,
          label: '自定义'
        }
      ],
      tableData: [],
      // 列表过滤条件
      filterData: {
        taskName: '',
        dept: '',
        personName: '',
        status: '0',
        accomplishType: '',
        date: null,
        acceptance: ''
      },
      statusOption: [
        {
          id: '2',
          label: '已完成'
        },
        {
          id: '1',
          label: '未完成'
        },
        {
          id: '0',
          label: '全部'
        }
      ],
      statusOvertimeTask: [
        {
          id: '2',
          label: '已超时'
        },
        {
          id: '3',
          label: '未超时'
        }
      ],
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      listRole: '',
      userIds: '',
      activeType: '',
      tableClickArry: [],
      treeData: [],
      treeProps: {
        children: 'children',
        label: (data, node) => {
          if (this.activeName == '0') {
            return data.planTypeName + '(' + data.sum + ')'
          } else {
            return data.baseName
          }
        },
        isLeaf: 'leaf'
      },
      useState: '',
      downLoadId: '',
      staging: null,
      checkDetail: {},
      showAcceptance: false,
      acceptanceLoding: false,
      selectionList: [], // 所有勾选项
      acceptanceList: [], // 可验收项
      deleteLsit: [], // 可删除项
      acceptanceStatus: [
        {
          id: '',
          name: '全部'
        },
        {
          id: '0',
          name: '未验收'
        },
        {
          id: '1',
          name: '验收通过'
        },
        {
          id: '2',
          name: '验收驳回'
        }
      ],
      expanded: [],
      // 导出loading
      exportLoading: false
    }
  },
  watch: {
    activeName(e, l) {
      this.treeData = []
      this.tableData = []
      if (e == '0') {
        this.getTemplateClassification()
      } else {
        this.getDeviceType()
      }
    }
  },
  created() {
    this.initEvent()
    // 设置日期默认值为当月1日至当前日期的23:59:59
    const firstDayOfMonth = moment().startOf('month')
    const endOfToday = moment().endOf('day') // 设置为当天的23:59:59
    this.filterData.date = [firstDayOfMonth.toDate(), endOfToday.toDate()]
  },
  activated() {
    this.initEvent()
  },
  mounted() {},
  methods: {
    initEvent() {
      if (this.$route.path.indexOf('/InspectionManagement') != -1) {
        this.systemType = '1'
      } else if (this.$route.path.indexOf('/MaintenanceManagement') != -1) {
        this.systemType = '2'
      } else if (this.$route.path.indexOf('/comInspectionManagement') != -1) {
        this.systemType = '3'
      }
      if (this.$route.query.activeName) {
        this.activeName = this.$route.query.activeName
        if (this.activeName == '0') {
          this.getTemplateClassification()
        } else {
          this.getDeviceType()
        }
      } else {
        this.getTemplateClassification()
      }
    },
    handleClick(tab) {
      this.$router.push({ query: { activeName: this.activeName } })
    },
    // 获取模板分类
    getTemplateClassification() {
      this.treeLoading = true
      this.$api
        .getTemplateType({
          systemIdentificationClassification: this.systemType,
          planOrTask: '0' // "1"：查询计划sum "0" ：查询任务sum
        })
        .then((res) => {
          if (res.code == '200') {
            if (this.activeName == '0') {
              if (res.data.length > 0) {
                this.treeData = res.data.map((i) => {
                  i.id = i.planTypeId
                  return i
                })
                this.treeData = transData(res.data, 'id', 'parentId', 'children')
                if (this.$route.query.typeId) {
                  this.checkItem.id = this.$route.query.typeId
                } else {
                  this.checkItem = this.treeData[0]
                }
                this.$nextTick(() => {
                  this.$refs.tree.setCurrentKey(this.checkItem.id)
                })
                this.getPlanList(this.checkItem.id)
              }
            }
          } else {
            this.$message.error(res.message || '获取失败！')
          }
          this.treeLoading = false
        })
    },
    // 设备分类列表
    getDeviceType() {
      this.treeLoading = true
      this.$api
        .getDeviceType({
          levelType: 3
        })
        .then((res) => {
          if (res.code == '200') {
            if (this.activeName == '1') {
              if (res.data.length > 0) {
                this.treeData = transData(res.data, 'id', 'parentId', 'children')
                if (this.$route.query.typeId) {
                  this.checkItem.id = this.$route.query.typeId
                  this.expanded = []
                  this.expendTreeNode(res.data, this.checkItem.id)
                } else {
                  this.checkItem = this.treeData[0]
                }
                this.$nextTick(() => {
                  this.$refs.tree.setCurrentKey(this.checkItem.id)
                })
                this.getPlanList(this.checkItem.id)
              }
            }
          } else {
            this.$message.error(res.message || '获取失败！')
          }
          this.treeLoading = false
        })
    },
    // 查找默认展开节点
    expendTreeNode(arr, id) {
      arr.forEach((i) => {
        if (i.parentId != '-1') {
          if (i.id == id) {
            this.expanded.unshift(i.parentId)
            this.expendTreeNode(arr, i.parentId)
          }
        } else {
          return
        }
      })
    },
    // 获取任务列表
    getPlanList(typeId) {
      const params = {
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        taskName: this.filterData.taskName || '',
        planPersonName: this.filterData.personName,
        accomplishType: this.filterData.accomplishType,
        departmentName: this.filterData.dept,
        taskStatus: this.filterData.status == '0' ? '' : this.filterData.status,
        planTypeId: typeId,
        taskStartTime: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        taskEndTime: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[1]).format('YYYY-MM-DD HH:mm:ss') : '',
        systemCode: this.systemType, // 1:设备 2: 保养 3：巡检
        inspectionReportId: this.filterData.acceptance
      }
      this.tableLoading = true
      this.$api.getTaskMaintaninList(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.sum
        }
        this.tableLoading = false
      })
    },
    filterNode(value, data) {
      if (this.activeName == '0') {
        if (!value) return true
        return data.planTypeName.indexOf(value) !== -1
      } else if (this.activeName == '1') {
        if (!value) return true
        return data.baseName.indexOf(value) !== -1
      }
    },
    nodeClick(data) {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.checkItem = data
      this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
      this.$router.push({
        query: {
          activeName: this.activeName,
          typeId: this.checkItem.planTypeId || this.checkItem.id
        }
      })
    },
    // 条件查询
    searchByCondition() {
      this.paginationData.currentPage = 1
      this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
    },
    // 重置查询条件
    resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      // 先重置所有非日期字段
      Object.assign(this.filterData, {
        taskName: '',
        dept: '',
        personName: '',
        status: '0',
        accomplishType: '',
        acceptance: ''
      })
      // 使用$nextTick确保在DOM更新后再设置日期值
      this.$nextTick(() => {
        // 设置日期默认值为当月1日至当前日期的23:59:59
        const firstDayOfMonth = moment().startOf('month')
        const endOfToday = moment().endOf('day') // 设置为当天的23:59:59
        this.filterData.date = [firstDayOfMonth.toDate(), endOfToday.toDate()]
        // 触发一次查询
        this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
      })
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeList.filter((i) => i.cycleType == row.cycleType)
      return item[0].label
    },
    // 列表操作
    operation(row, type) {
      if (type == 'edit') {
        sessionStorage.setItem('row', JSON.stringify(row))
        this.$router.push({
          path: 'taskManagement/taskPointEdit',
          query: {
            taskId: row.id,
            row,
            type,
            activeName: this.activeName
          }
        })
      } else if (type == 'detail') {
        this.tableLoading = true
        sessionStorage.setItem('row', JSON.stringify(row))
        this.$router.push({
          path: 'taskManagement/taskDetail',
          query: {
            taskId: row.id,
            row,
            type,
            activeName: this.activeName
          }
        })
      // 需求 3943 需要去除状态修改
      // else if (type == 'status') {
      //   this.statusVisible = true
      //   this.checkDetail = row
      // }
      } else if (type == 'delete') {
        this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.$api.deleteTask({ id: row.id }, { 'operation-type': 3, 'operation-name': row.taskName, 'operation-id': row.id }).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: '删除成功'
                })
                if (this.activeName == '0') {
                  this.getTemplateClassification()
                } else {
                  this.getDeviceType()
                }
                this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
              }
            })
          })
          .catch(() => {})
      } else if (type == 'check') {
        this.acceptance(row)
      }
    },
    // 导出
    exportExcel() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        platformFlag: 1,
        planTypeId: this.checkItem.planTypeId || this.checkItem.id,
        taskName: this.filterData.taskName || '',
        planPersonName: this.filterData.personName,
        departmentName: this.filterData.dept,
        taskStatus: this.filterData.status == '0' ? '' : this.filterData.status,
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        taskStartTime: this.filterData.date[0] ? moment(this.filterData.date[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        taskEndTime: this.filterData.date[1] ? moment(this.filterData.date[1]).format('YYYY-MM-DD HH:mm:ss') : '',
        systemCode: this.systemType, // 1:设备 2: 保养 3：巡检
        inspectionReportId: this.filterData.acceptance
      }
      if (this.selectionList.length > 0) {
        params.ids = this.selectionList.map((i) => i.id).join(',')
      } else {
        params.ids = this.tableData.map((i) => i.id).join(',')
      }
      const formData = new FormData()
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + 'planTaskNew/taskExport',
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('导出失败')
        })
    },
    // 导出明细
    exportDetail() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        platformFlag: 1,
        planTypeId: this.checkItem.planTypeId || this.checkItem.id,
        taskName: this.filterData.taskName || '',
        planPersonName: this.filterData.personName,
        departmentName: this.filterData.dept,
        taskStatus: this.filterData.status == '0' ? '' : this.filterData.status,
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        taskStartTime: this.filterData.date[0] ? moment(this.filterData.date[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        taskEndTime: this.filterData.date[1] ? moment(this.filterData.date[1]).format('YYYY-MM-DD HH:mm:ss') : '',
        systemCode: this.systemType, // 1:设备 2: 保养 3：巡检
        inspectionReportId: this.filterData.acceptance
      }
      if (this.selectionList.length > 0) {
        params.ids = this.selectionList.map((i) => i.id).join(',')
      } else {
        params.ids = this.tableData.map((i) => i.id).join(',')
      }
      const formData = new FormData()
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      this.exportLoading = true
      axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + 'planTaskExport/exportTaskDetails',
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'operation-type': 4
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('导出失败')
        })
        .finally(() => (this.exportLoading = false))
    },
    handleClose() {
      this.statusVisible = false
    },
    saveStatus() {
      this.statusLoding = true
      this.$refs.changeStatusDialog.changeStatusSave()
    },
    updataList() {
      this.statusLoding = false
      this.statusVisible = false
      this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
    },
    handleSelectionChange(val) {
      this.selectionList = val
    },
    acceptance(row) {
      if (row) {
        this.acceptanceList = [row]
        this.showAcceptance = true
      } else {
        this.acceptanceList = this.selectionList.filter((i) => i.taskStatus == '2' && (!i.inspectionReportId || i.inspectionReportId == 0))
        if (this.acceptanceList.length > 0) {
          if (this.acceptanceList.length != this.selectionList.length) {
            this.$message({
              type: 'info',
              message: '未完成任务、已验收任务不可验收',
              customClass: 'messageIndex'
            })
          }
          this.$refs.materialTable.clearSelection()
          this.acceptanceList.forEach((i) => {
            this.$refs.materialTable.toggleRowSelection(i)
          })
          this.showAcceptance = true
        } else {
          this.$refs.materialTable.clearSelection()
          this.$message({
            type: 'info',
            message: '当前勾选任务未完成，或已被全部验收',
            customClass: 'messageIndex'
          })
        }
      }
    },
    handleCloseAcceptance() {
      this.showAcceptance = false
    },
    batchDelete() {
      this.deleteLsit = this.selectionList.filter((i) => i.taskStatus == 1 && moment().isBefore(i.taskEndTime))
      if (this.deleteLsit.length > 0) {
        if (this.deleteLsit.length != this.selectionList.length) {
          this.$message({
            type: 'info',
            message: '已完成或已超期的任务无法删除',
            customClass: 'messageIndex'
          })
        }
        this.$refs.materialTable.clearSelection()
        this.deleteLsit.forEach((i) => {
          this.$refs.materialTable.toggleRowSelection(i)
        })
        const idArr = this.deleteLsit.map((i) => i.id)
        this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.tableLoading = true
            this.$api.deleteTask({ id: idArr.join(',') }, { 'operation-type': 3 }).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: '删除成功'
                })
                this.tableLoading = false
                if (this.activeName == '0') {
                  this.getTemplateClassification()
                } else {
                  this.getDeviceType()
                }
                this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
              }
            })
          })
          .catch(() => {})
      } else {
        this.$refs.materialTable.clearSelection()
        this.$message({
          type: 'info',
          message: '已完成或已超期的任务无法删除',
          customClass: 'messageIndex'
        })
      }
    },
    dwonLoadFile(name, url) {
      fetch(url).then((res) =>
        res.blob().then((blob) => {
          var a = document.createElement('a')
          var url = window.URL.createObjectURL(blob)
          a.href = url
          a.download = name
          a.click()
          window.URL.revokeObjectURL(url)
        })
      )
    },
    handleDateChange(val) {
      console.log('日期变化:', val)
      // 确保数据变化反映到视图中
      this.$forceUpdate()
      // 如果需要在日期变化时自动查询，可以取消下面这行的注释
      // this.getPlanList(this.checkItem.planTypeId || this.checkItem.id)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.tabsItem {
  padding: 8px 0;
  width: 200px;
  margin: 0 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #5b5b5b;
  cursor: pointer;
  .item {
    width: 200px !important;
  }
  > span {
    width: 200px !important;
    margin: 0 auto !important;
  }
}
.tabsItem:hover {
  color: #5188fc;
}
:deep(.el-tabs--left) {
  width: 200px;
  margin: 20px auto 0;
}
:deep(.el-table__row) {
  :last-child > .cell {
    display: flex;
    > * {
      margin-right: 10px;
    }
  }
}
.special_box {
  height: 100%;
  display: flex;
  .left {
    width: 246px;
    min-width: 14%;
    height: calc(100% - 30px);
    border-radius: 10px;
    background-color: #fff;
    margin: 15px;
    .leftCon {
      padding: 10px;
      height: 100%;
      // .toptip {
      //   box-sizing: border-box;
      //   padding-left: 26px;
      //   height: 50px;
      //   width: 100%;
      //   line-height: 50px;
      //   text-align: left;
      //   border-bottom: none;
      //   font-weight: 600;
      // }
      .tabsBox {
        text-align: center;
        margin: 10px 0;
        height: calc(100% - 100px);
        overflow: auto;
        ::v-deep .el-tree-node__content {
          width: 100%;
          text-align: left;
          .custom-tree-node {
            width: calc(100% - 24px) !important;
            .treeLabel {
              display: inline-block;
              width: 100%;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
          }
        }
      }
    }
  }
  .content_box {
    width: calc(100% - 276px);
    margin: 15px 0;
    border-radius: 10px;
    padding: 20px 25px 25px;
    background: #fff;
    display: flex;
    flex-direction: column;
    .top_content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    .table_list {
      .disable {
        color: #414653;
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #ccced3;
        }
      }
      .enable {
        color: #08cb83;
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #08cb83;
        }
      }
      .acceptanceText {
        cursor: pointer;
      }
      .acceptanceColor0 {
        color: #414653;
      }
      .acceptanceColor1 {
        color: #08cb83;
      }
      .acceptanceColor2 {
        color: #f56c6c;
      }
    }
  }
}
:deep(.el-tabs__nav) {
  width: 100%;
  .el-tabs__item {
    padding: 0 10px;
    width: 50%;
    text-align: center;
  }
}
:deep(.changeStatusDialog) {
  padding: 10px 15px;
}
:deep(.acceptanceDialog) {
  padding: 10px 15px;
  .el-dialog__body {
    padding: 20px;
  }
}
</style>
<style lang="scss">
.messageIndex {
  z-index: 3000 !important;
}
.recordContent {
  .recordWrap {
    display: flex;
    margin: 5px 0;
    .recordTitle {
      width: 70px;
      text-align: right;
      display: block;
    }
    .textRecord {
      width: calc(100% - 70px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .recordWrap:nth-child(n + 5) {
    text-align: right;
  }
}
</style>
