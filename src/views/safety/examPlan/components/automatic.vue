<template>
  <div class="inner">
    <div class="top">
      <span>考题配置
        <span class="examSum">总共题数：{{ questionsScore.nums || 0 }}题 总分数：{{ questionsScore.sumScore || 0 }}分</span></span>
      <el-button class="footer" type="primary" @click="addClassHour">添加题目</el-button>
    </div>
    <div class="contener">
      <el-form label-width="140px" :model="questionsInfo" :rules="questionsRlues" :inline="true" ref="autoExamInfo"
        class="demo-form-inline">
        <div class="testItem" v-for="(i, index) in questionsInfo.questionsList" :key="index">
          <div style="display: flex;">
            <el-form-item label="题目来源" :prop="`questionsList.${index}.courseId`" :rules="questionsRlues.courseId">
              <el-select v-model="i.courseId" placeholder="请选择课程" style="width: 460px;"
                @change="changeCourse($event, index)">
                <el-option v-for="item in topicList" :key="item.id" :label="item.courseName" :value="item.id">
                </el-option>
              </el-select>

              <!-- <span class="note">( 注当前课时包含单选题{{getTypeNum(1,index)}}道、多选题{{getTypeNum(2,index)}}道、判断题{{getTypeNum(3,index)}}道 )</span> -->
            </el-form-item>
            <div style="display: flex; margin-top: 8px;">
              <div v-if="index != 0"><i class="el-icon-delete" @click="delectMu(index)"></i></div>
              <div class="note">( 注当前课时包含单选题{{ getTypeNum(1, index) }}道、多选题{{ getTypeNum(2, index) }}道、判断题{{
          getTypeNum(3, index) }}道、 填空题{{getTypeNum(4, index) }}道、 简答题{{getTypeNum(5, index) }}道
                )
              </div>
            </div>
          </div>

          <div class="questionsType">
            <div v-for="(k, ind) in i.questionTypes" :key="ind">
              <span class="spanClass">
                {{  k.type == "1" ? "单选题" : k.type == "2" ? "多选题" : k.type == "3"? "判断题":k.type == "4"?"填空题":"简答题"}}
              </span>
              <el-form-item :prop="`questionsList.${index}.questionTypes.${ind}.num`" :rules="questionsRlues.num"
                label="题目个数" class="itemNum">
                <el-input type="number" placeholder="请输入" min="1" style="width: 150px" v-model="k.num"
                  @change="numChange(i, ind, index)">
                  <template slot="append">个</template>
                </el-input>
              </el-form-item>
              <el-form-item :prop="`questionsList.${index}.questionTypes.${ind}.score`" :rules="questionsRlues.score"
                label="每小题分数">
                <el-input type="number" placeholder="请输入" min="1" style="width: 150px" v-model="k.score"
                  @change="scoreChange(i, ind)">
                  <template slot="append">分</template>
                </el-input>
              </el-form-item>
              <el-form-item v-if="k.type == '4'" :prop="`questionsList.${index}.questionTypes.${ind}.blankNum`"
                :rules="questionsRlues.blankNum" label="每题填空数">
                <el-input type="number" placeholder="请输入" min="1" style="width: 150px" v-model="k.blankNum"
                  @change="blankNumChange(i, ind)">
                  <template slot="append">个</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>

  </div>
</template>
<script>
  import axios from "axios";
import { toNumber } from "lodash";
  export default {
    props: {
      autoInfo: {
        type: Object,
        default: {}
      },
      scoreInfo: {
        type: Object,
        default: {}
      }
    },
    data() {
      let validateNum = (rule, value, callback) => {
        // if (!validateUserName(value)) {
        //   callback(new Error("用户名不正确，请重新输入！"));
        // } else {
        callback();
        // }
      };
      return {
        topicList: [],
        checked: false,
        questionsScore: {
          nums: 0,
          sumScore: 0
        },
        questionsInfo: {
          questionsList: [{
            questionTypes: [{
                type: "1",
                num: 0,
                score: 0,
                blankNum: 0
              },
              {
                type: "2",
                num: 0,
                score: 0,
                blankNum: 0
              },
              {
                type: "3",
                num: 0,
                score: 0,
                blankNum: 0
              },
              {
                type: "4",
                num: 0,
                score: 0,
                blankNum: 0
              },
              {
                type: "5",
                num: 0,
                score: 0,
                blankNum: 0
              },

            ],
            courseId: '',
            questionsNum: []
          }, ],
        },
        questionsRlues: {
          courseId: [{
            required: true,
            message: "请输入课时名称",
            trigger: "change"
          }, ],
          num: [{
            validator: validateNum,
            trigger: "blur"
          }, ]
        }
      };
    },
    created() {
      this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"))
      this.questionsInfo = this.autoInfo
      console.log(this.questionsInfo,'questionsInfo');
      
      this.questionsScore = this.scoreInfo
    },
    methods: {
      // 获取科目分类下的课程
      getCourseList(val) {
        this.topicList = []
        this.$api.allCourseList().then((res) => {
          if (res.code == 200) {
            this.topicList = res.data;
          } else {
            this.$message.error(res.msg);
          }
        });
      },
      // 添加题目来源
      addClassHour() {
        let obj = {
          questionTypes: [{
              type: "1",
              num: "",
              score: "",
              blankNum: ""
            },
            {
              type: "2",
              num: "",
              score: "",
              blankNum: ""
            },
            {
              type: "3",
              num: "",
              score: "",
              blankNum: ""
            },
            {
              type: "4",
              num: "",
              score: "",
              blankNum: ""
            },
            {
              type: "5",
              num: "",
              score: "",
              blankNum: ""
            },
          ],
          courseId: '',
          questionsNum: []
        };
        this.questionsInfo.questionsList.push(obj);
      },
      delectMu(index) {
        if (index > -1) {
          this.questionsInfo.questionsList.splice(index, 1)
        }
      },
      // 获取所有课程的数量
      getAllNums() {
        this.questionsInfo.questionsList.forEach((item, index) => {
          this.$api.courseQuestionNum({
            courseId: item.courseId,
            scoringType: '0'
          }).then((res) => {
            if (res.code == '200') {
              item.questionsNum = res.data
            } else {
              this.$message.error(res.msg)
            }
          })

        })
      },
      // 选择试题
      changeCourse(val, index) {
        this.$api.courseQuestionNum({
          courseId: val,
          scoringType: '0'
        }).then((res) => {
          if (res.code == '200') {
            this.questionsInfo.questionsList[index].questionsNum = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
      },
      getTypeNum(val, index) {
        if (this.questionsInfo.questionsList[index].questionsNum) {
          let obj = this.questionsInfo.questionsList[index].questionsNum.find(i => i.type == val)
          if (obj) {
            return obj.num || 0
          } else {
            return 0
          }
        }
      },
      // 试题个数选择
      numChange(val, ind, index) {
        if (!val.courseId) {
          return this.$message.error("请先选择题目来源");
        }
        let obj = this.questionsInfo.questionsList[index].questionsNum.find(
          (i) => i.type == val.questionTypes[ind].type
        );
        if (!obj) {
          return this.$message.error("该课程下没有该类型的题目");
        }
        if (obj && val.questionTypes[ind].num > obj.num) {
          return this.$message.error("超出该类型的题目个数！");
        }
        //如果题型为填空题，并且每题填空有值时，调取接口
        if(val.questionTypes[ind].type =='4' && val.questionTypes[ind].blankNum){
          let params = {
            courseId:val.courseId,  //课程id
            blankTotal: toNumber(val.questionTypes[ind].num) || 0, //此课程需要的填空题数量
            blankNum:  toNumber(val.questionTypes[ind].blankNum) //填空题填空个数
          }
          this.$api.checkFillBlanksNum(params).then(res=>{
            if(res.code=='200'){
              
            }else{
              val.questionTypes[ind].num =''
              this.$message.error(res.msg)
            }
          })
        }
        this.questionsScore.nums = 0
        this.questionsScore.sumScore = 0
        this.questionsInfo.questionsList.forEach(item => {
          item.questionTypes.forEach(k => {
            this.questionsScore.nums += k.num == '' ? 0 : parseInt(k.num)
            this.questionsScore.sumScore += k.score == '' ? 0 : parseInt(k.num) * parseInt(k.score)
          })
        })
      },
      // 分数选择
      scoreChange() {
        this.questionsScore.sumScore = 0
        this.questionsInfo.questionsList.forEach(item => {
          item.questionTypes.forEach(k => {
            this.questionsScore.sumScore += k.score == '' ? 0 : parseInt(k.num) * parseInt(k.score)
          })
        })
      },
      // 填空个数选择
      blankNumChange(val, ind,){
        if(!val.questionTypes[ind].num){
          val.questionTypes[ind].blankNum =''
          return this.$message.error('请先输入填空题题目个数！')
        }
        let params = {
          courseId:val.courseId,  //课程id
          blankTotal: toNumber(val.questionTypes[ind].num) || 0, //此课程需要的填空题数量
          blankNum:  toNumber(val.questionTypes[ind].blankNum) //填空题填空个数
        }
        this.$api.checkFillBlanksNum(params).then(res=>{
          if(res.code=='200'){
            
          }else{
            val.questionTypes[ind].blankNum =''
            this.$message.error(res.msg)
          }
        })
      },
      // 提交考试
      submit(type) {
        this.$refs.autoExamInfo.validate((valid) => {
          if (valid) {
            console.log(this.questionsInfo, 'this.questionsInfo');
            // this.questionsInfo.questionsList.forEach((el) => {
            //   el.questionTypes()
            //   console.log(el,'133SJLKSMXZ');
            // })
            // return
            this.$emit('submitAuto', this.questionsInfo, this.questionsScore, type)
          }
        });
      },
    },
  };

</script>
<style lang="scss" scoped>
  .inner {
    background-color: #fff;
    height: calc(100% - 140px);
    padding: 16px 22px;

    .top {
      display: flex;
      justify-content: space-between;

      .examSum {
        font-size: 14px;
        color: #7f848c;
      }

      .passScore {
        font-size: 14px;
      }
    }

    .contener {
      background-color: #faf9fc;
      height: calc(100% - 50px);
      overflow: auto;
      padding: 16px;

      // margin-bottom: 16px;
      .testItem {
        height: 400px;
        background-color: #fff;
        font-size: 14px;
        padding: 24px;
        margin-bottom: 16px;
        border-radius: 4px;

        .note {
          color: #ccced3;
          margin-left: 24px;
        }

        .questionsType {
          // display: flex;
          margin: 0 0 0 90px;

          >div {
            // flex: 1;
            margin-right: 24px;
          }
          .spanClass {
            display: inline-block;
            margin-top: 9px;
          }
          .el-form-item__content .el-input-group  {
            margin-top: 3px;
          }
        }
      }
    }
  }

  ::v-deep .el-form--label-top .el-form-item__label {
    float: left !important;
  }

  // .itemNum {
  //   ::v-deep .el-form-item__label {
  //     width: 10px !important;
  //   }
  // }

</style>
