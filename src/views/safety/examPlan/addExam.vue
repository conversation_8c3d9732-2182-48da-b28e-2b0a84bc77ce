<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            {{ typeStr == "0" ? "自动组卷" : "手动组卷" }}
          </span>
        </div>
      </div>
      <div class="courseContent">
        <div class="courseTab">
          <div class="left activeLeft" @click="upStep">1.基础内容</div>
          <div class="centerLeft"></div>
          <div v-if="active == '1'" class="centerRight"></div>
          <div :class="['right', active == 1 ? 'activeRight' : '']" @click="nextStep">
            2.考试配置
          </div>
        </div>
        <div class="courseInfo" v-if="active == '0'">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            基础信息
          </div>
          <el-form label-width="160px" :model="formInfo" :rules="rules" ref="formInfo" class="formInfo">
            <el-row>
              <el-col :span="12">
                <el-form-item label="试卷名称" prop="name">
                  <el-input v-model="formInfo.name" placeholder="请输入试卷名称" show-word-limit maxlength="30"
                    style="width: 300px"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属科目" prop="subjectId">
                  <el-cascader v-model="formInfo.subjectId" clearable class="sino_sdcp_input mr15" style="width: 300px"
                    :options="subjectList" :props="props" placeholder="请选择类型" @change="hangdleChange"></el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="分配方式" prop="stuOrOrg">
                  <el-select v-model="formInfo.stuOrOrg" placeholder="请选择分配方式" style="width: 300px">
                    <el-option v-for="item in stuOrOrgList" :key="item.id" :label="item.label" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="formInfo.stuOrOrg == '0'">
                <el-form-item label="考试学员" class="">
                  <span style="color: red; margin-left: -80px">*</span>
                  <div class="set_select_width" @click="showUserDialog">
                    <template v-if="userNewList.length">
                      <span v-for="(item, index) in userNewList" :key="index">
                        {{ item }}
                        <i class="el-icon-error" @click.stop="deleteTag(index, 'user')"></i>
                      </span>
                    </template>
                    <p v-else>请选择人员</p>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="formInfo.stuOrOrg == '1'">
                <el-form-item label="组织" prop="orgId">
                  <el-select v-model="formInfo.orgId" placeholder="请选择组织" style="width: 300px" multiple>
                    <el-option v-for="item in deptAllList" :key="item.id" :label="item.teamName" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属部门" prop="">
                  <el-cascader v-model="formInfo.deptIds" placeholder="请选择课程公开范围" :options="deptList" :props="deptTree"
                    :show-all-levels="false" clearable filterable collapse-tags style="width: 300px">
                  </el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="考试期限" prop="timeLine">
                  <el-date-picker v-model="formInfo.timeLine" type="datetimerange" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" :default-time="['00:00:00', '23:59:59']"
                    style="width: 400px">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="答题时长" prop="duration">
                  <el-input type="number" placeholder="请输入答题时长" min="1" style="width: 300px"
                    v-model="formInfo.duration" @keyup.native="proving">
                    <template slot="append">分钟</template>
                  </el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="考试不通过" prop="">
                  <el-radio-group v-model="formInfo.rejectDeal">
                    <el-radio v-for="item in noPassList" :key="item.id" :label="item.id">{{ item.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="评卷方式" prop="">
                  <el-tooltip class="item" effect="dark" placement="top">
                    <template #content>
                      <div class="tooltipDiv">1.选择自动评卷时，仅支持选择配置了得分规则的题目</div>
                      <div class="tooltipDiv">2.选择人工评卷时，考试试题支持选择所有题型，人工评卷时仅需要对填空、简答题评分，单选、多选、判断题自动评分</div>
                    </template>
                    <i class="el-icon-question iconQuestion"></i>
                  </el-tooltip>
                  <el-radio-group v-model="formInfo.scoringType">
                    <el-radio v-for="item in markingMethod" :key="item.id" :label="item.id">{{ item.label }}</el-radio>
                  </el-radio-group>

                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="考试后答案显示规则" prop="">
                  <el-radio-group v-model="formInfo.answerDisplayType ">
                    <el-radio v-for="item in displayRule" :key="item.id" :label="item.id">{{ item.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="评卷人员" prop="scoringPersonType" v-if="formInfo.scoringType=='1'">
                  <el-select v-model="formInfo.scoringPersonType" placeholder="请选择评卷人员或组织" style="width: 300px">
                    <el-option label="人员" value="0"></el-option>
                    <el-option label="组织" value="1"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="formInfo.scoringPersonType == '0'&& formInfo.scoringType=='1'">
                <el-form-item label="评卷人员" class="">
                  <span style="color: red; margin-left: -80px">*</span>
                  <div class="set_select_width" @click="showScorDialog">
                    <template v-if="scoringList.length">
                      <span v-for="(item, index) in scoringList" :key="index">
                        {{ item }}
                        <i class="el-icon-error" @click.stop="deleteScor(index, 'user')"></i>
                      </span>
                    </template>
                    <p v-else>请选择人员</p>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="formInfo.scoringPersonType == '1'&&formInfo.scoringType=='1'">
                <el-form-item label="评卷组织" prop="scoringOrgId">
                  <el-select v-model="formInfo.scoringOrgId" placeholder="请选择评卷组织" style="width: 300px" multiple>
                    <el-option v-for="item in deptAllList" :key="item.id" :label="item.teamName" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="试卷描述" prop="">
                  <el-input v-model="formInfo.disc" type="textarea" show-word-limit maxlength="200" placeholder="请输入"
                    :autosize="{ minRows: 4, maxRows: 6 }" style="width: 400px"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <el-form-item label="考试通过自动发放证书" prop="">
              <el-select v-model="formInfo.credentialId" placeholder="请选择完成获取证书" style="width: 300px">
                <el-option v-for="item in certificateList" :key="item.id" :label="item.certificateName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item> -->
          </el-form>
        </div>
        <div class="courseList" v-if="active == '1'">
          <div class="examInfo">
            考试组卷
            <div class="examContent">
              <div class="itemInfo">
                <span class="title">试卷名称：</span>
                <span class="value">{{ formInfo.name }}</span>
              </div>
              <div class="itemInfo">
                <span class="title">所属科目：</span>
                <span class="value">{{ subjectNames }}</span>
              </div>
              <div class="itemInfo">
                <span class="title">答题时长：</span>
                <span class="value">{{ formInfo.duration }}分钟</span>
              </div>
              <div class="itemInfo">
                <span class="title">考试期限：</span>
                <span class="value">{{ formInfo.startTime }}至{{ formInfo.endTime }}</span>
              </div>
              <div class="fraction">
                <span style="color: red">*</span>
                <span style="margin-right: 16px">通过分数</span>
                <el-input type="number" placeholder="请输入通过分数" min="1" style="width: 300px" v-model="formInfo.passScore" @keyup.native="proving">
                  <template slot="append">分</template>
                </el-input>
              </div>
            </div>
          </div>
          <automatic ref="automatic" v-if="typeStr == '0'" :autoInfo="autoInfo" :scoreInfo="scoreInfo"
            @submitAuto="submitAuto">
          </automatic>
          <hand-movement ref="handMovement" v-if="typeStr == '1'" :noAutoList="noAutoList" :scoreInfo="scoreInfo"
            :subjectId="formInfo.subjectId" @submitAuto="submitAuto"></hand-movement>
        </div>
      </div>
      <!-- 学员弹窗 -->
      <people-dialog v-if="peopleDialog" :peopleFlag='peopleFlag' ref="userDialogRef" :peopleDialog="peopleDialog"
        @closeDialog="closeDialog" @sureDialogUser="sureDialogUser" @sureDialogScoring="sureDialogScoring">
      </people-dialog>
    </div>
    <div slot="footer">
      <el-button v-if="active == '0'" type="primary" @click="nextStep">下一步</el-button>
      <el-button type="primary" plain @click="onCancel">取消</el-button>
      <el-button v-if="active == '1'" type="primary" plain @click="upStep">上一步</el-button>
      <el-button v-if="active == '1'" type="primary" @click="addExam('0')">保存草稿</el-button>
      <el-button v-if="active == '1'" type="primary" @click="addExam('2')">发布考试</el-button>
    </div>
  </PageContainer>
</template>
<script>
  import axios from "axios";
  import automatic from "./components/automatic.vue";
  import handMovement from "./components/handMovement.vue";
  import PeopleDialog from './components/peopleDialog.vue';

  export default {
    components: {
      automatic,
      handMovement,
      PeopleDialog
    },
    data() {
      return {
        peopleFlag: '0',
        props: {
          children: "children",
          label: "name",
          value: "id",
          // checkStrictly: true,
          emitPath: false,
        },
        deptTree: {
          children: "children",
          label: "teamName",
          value: "id",
          multiple: true,
          emitPath: false,
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          }
        },
        routeInfo: {},
        id: "",
        typeStr: "",
        active: "0",
        formInfo: {
          name: "",
          subjectId: null,
          stuOrOrg: '',
          orgId: '',
          deptIds: "",
          timeLine: [],
          startTime: '',
          endTime: '',
          rejectDeal: '1',
          duration: "", // 答题时长
          credentialId: "", //证书id
          disc: "", //描述
          passScore: '', //通过分数
          answerDisplayType: "0",
          scoringType: "0",
          scoringPersonType: "",
          scoringOrgId: "", //评卷组织id
        },
        subjectNames: '',
        subjectList: [],
        subjectAllList: [],
        deptList: [],
        deptAllList: [],
        certificateList: [],
        stuOrOrgList: [{
            id: '0',
            label: '人员' //解决bug36656,将学生改为人员
          },
          {
            id: '1',
            label: '组织'
          }
        ],
        noPassList: [{
            id: "1",
            label: "不补考",
          },
          {
            id: "2",
            label: "自动组卷补考",
          },
          {
            id: "3",
            label: "手动派发补考试卷",
          },
        ],
        markingMethod: [{
            id: "0",
            label: "自动评卷",
          },
          {
            id: "1",
            label: "人工评卷",
          },

        ],
        displayRule: [{
            id: "0",
            label: "不显示",
          },
          {
            id: "1",
            label: "交卷后立即显示",
          },
          {
            id: "2",
            label: "考试期限结束后显示",
          },
        ],
        rules: {
          name: [{
              required: true,
              message: "请输入试卷名称",
              trigger: "blur"
            },
            {
              min: 1,
              max: 30,
              message: "长度在 1 到 30 个字符",
              trigger: "blur",
            },
          ],
          subjectId: [{
            required: true,
            message: "请选择所属科目",
            trigger: "change"
          }, ],
          stuOrOrg: [{
            required: true,
            message: "请选择分配方式",
            trigger: "change"
          }, ],
          orgId: [{
            required: true,
            message: "请选择所属组织",
            trigger: "change"
          }, ],
            scoringOrgId: [{
            required: true,
            message: "请选择评卷组织",
            trigger: "change"
          }, ],
          scoringPersonType: [{
            required: true,
            message: "请选择评卷人员或组织",
            trigger: "change"
          }, ],
          timeLine: [{
            required: true,
            message: "请选择考试期限",
            trigger: "change"
          }, ],
          duration: [{
            required: true,
            message: "请输入答题时长",
            trigger: "blur"
          }, ],
        },
        peopleDialog: false,
        userList: [],
        scorUserList: [],
        userNewList: [],
        scoringList: [],
        autoInfo: {
          questionsList: [{
            questionTypes: [{
                type: "1",
                num: '',
                score: '',
                blankNum: ""
              },
              {
                type: "2",
                num: '',
                score: '',
                blankNum: ""
              },
              {
                type: "3",
                num: '',
                score: '',
                blankNum: ""
              },
              {
                type: "4",
                num: '',
                score: '',
                blankNum: ""
              },
              {
                type: "5",
                num: '',
                score: '',
                blankNum: ""
              },
            ],
            courseId: '',
            questionsNum: []
          }, ],
        },
        scoreInfo: {
          nums: 0,
          sumScore: 0
        },
        noAutoList: [],
        userAllList: []
      };
    },
    created() {
      this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
      console.log(this.routeInfo);

      this.getCertificateList()
      this.getdeptList();
      this.getTblleList();
      this.id = this.$route.query.id || "";
      this.typeStr = this.$route.query.type || "";
      if (this.id) {
        this.getTableList()
      }
    },
    watch: {
      'formInfo.timeLine'(val) {
        if (val.length > 0) {
          this.formInfo.startTime = val[0];
          this.formInfo.endTime = val[1];
        } else {
          this.formInfo.startTime = "";
          this.formInfo.endTime = "";
        }
      },
    },
    methods: {
      /** 输入框输入限制 */
      proving(e) {
        if (e.target.value && e.target.value.length == 1) {
          e.target.value = e.target.value.toString().replace(/[^1-9]/g, '') // 只能输入正整数.replace(/[^1-9]/g, '')
        } else {
          e.target.value = e.target.value.toString().replace(/[^0-9]/g, '') // 只能输入正整数.replace(/[^1-9]/g, '')
        }
      },
      // 获取人员列表
      getTableList() {
        let data = {
          currentPage: 1,
          pageSize: 9999,
          controlTeamId: ''
        }
        this.$api.getControlTeamUserListLaboratory(data).then((res) => {
          if (res.code == 200) {
            this.userAllList = res.data.list
            if (this.id) {
              this.getDetails()
            }
          } else {
            this.$message.error(res.message)
          }
        })
      },
      // 获取证书列表
      getCertificateList() {
        this.$api.certificateList({
          pageNo: 1,
          pageSize: 9999
        }).then((res) => {
          if (res.code == 200) {
            this.certificateList = res.data.records;
          } else {
            this.$message.error(res.message);
          }
        });
      },
      getDetails() {
        this.$api.examPlanDetails({
          id: this.id
        }).then(res => {
          if (res.code == '200') {
            this.formInfo = res.data.saveDto
            this.hangdleChange(this.formInfo.subjectId)
            this.formInfo.orgId = this.formInfo.orgId ? this.formInfo.orgId.split(',') : []
            this.formInfo.scoringOrgId = this.formInfo.scoringOrgId ? this.formInfo.scoringOrgId.split(',') : []
            this.formInfo.deptIds = this.formInfo.deptIds ? this.formInfo.deptIds.split(',') : []
            this.$set(this.formInfo, 'timeLine', [this.formInfo.startTime, this.formInfo.endTime]);
            
            // 考试人员回显
            let studentIdList = this.formInfo.studentIds.split(',')
            let scoringStudentIds = this.formInfo.scoringStudentIds.split(',')
            this.userList = []
            this.scorUserList = []
            studentIdList.forEach(i => {
              this.userAllList.forEach(k => {
                if (i == k.id) {
                  this.userList.push(k)
                }
              })
            })
            scoringStudentIds.forEach(i => {
              this.userAllList.forEach(k => {
                if (i == k.id) {
                  this.scorUserList.push(k)
                }
              })
            })
            const userNewList = this.userList.map(item => item.name)
            const scoringList = this.scorUserList.map(item => item.name)
            this.userNewList = userNewList
            this.scoringList = scoringList
            if (this.typeStr == '0') {
              res.data.saveDto.auto.forEach((k, index) => {
                this.$api.courseQuestionNum({
                  courseId: k.courseId,
                  scoringType: this.formInfo.scoringType
                }).then((res) => {
                  if (res.code == '200') {
                    k.questionsNum = res.data
                  } else {
                    this.$message.error(res.msg)
                  }
                })
              })
              let autoData = res.data.saveDto.auto
              this.autoInfo.questionsList = [...autoData]
              this.$forceUpdate()

            } else {
              res.data.saveDto.noAutoQuestions.forEach(item => {
                item.options = JSON.parse(item.options)
              })
              this.noAutoList = res.data.saveDto.noAutoQuestions
            }
            this.scoreInfo.sumScore = res.data.saveDto.score
            this.scoreInfo.nums = res.data.saveDto.count
          } else {
            this.$message.error(res.msg)
          }
        })
      },
      hangdleChange(val) {
        let obj = this.subjectAllList.find(i => i.id == val)
        this.subjectNames = obj.free1
        // if (this.typeStr == '0') {
        //   this.$nextTick(() => {
        //     console.log(this.typeStr,'');
        //     console.log(this.$refs.automatic, 'this.$refs.automatic--------');
        //   })
        // }
      },
      getTblleList() {
        let data = {
          pageNo: 1,
          pageSize: 999,
        };
        this.$api.subjectListAll(data).then((res) => {
          if (res.code == 200) {
            this.subjectAllList = res.data
            this.subjectList = this.$tools.transData(
              res.data,
              "id",
              "parentId",
              "children"
            )
          } else {
            this.$message.error(res.message);
          }
        });
      },
      // 获取组织
      getdeptList() {
        this.$api.getDeptListLaboratory({}).then((res) => {
          this.deptList = this.$tools.transData(
            res.data.list,
            "id",
            "parentId",
            "children"
          );
          this.deptAllList = res.data.list
        });
      },
      // 上一步
      upStep() {
        this.active = "0";
        if (this.typeStr == '0') {
          this.autoInfo = this.$refs.automatic.questionsInfo
          this.scoreInfo = this.$refs.automatic.questionsScore;
        } else {
          this.noAutoList = this.$refs.handMovement.questionsList
          this.scoreInfo = this.$refs.handMovement.questionsScore
        }
      },
      // 下一步
      nextStep() {
        this.$refs.formInfo.validate((valid) => {
          if (valid) {
            if (this.formInfo.stuOrOrg == '0' && !this.userNewList.length) {
              return this.$message.error("请选择考试学员");
            }
            if (this.formInfo.scoringPersonType == '0' && this.formInfo.scoringType == '1' && !this.scoringList.length) {
              return this.$message.error("请选择评卷人员");
            }
            this.active = "1";
            if (this.typeStr == '0') {
              this.$nextTick(() => {
                //解决bug36591,编辑考试计划课程来源不回显问题，点击考试设置值被清空了，所以删除
                //   this.$refs.automatic.questionsScore = {
                //     nums: 0,
                //     sumScore: 0
                //   },
                //   this.$refs.automatic.questionsInfo.questionsList = [
                //     {
                //       questionTypes: [
                //         {
                //           type: "1",
                //           num: '',
                //           score: '',
                //         },
                //         {
                //           type: "2",
                //           num: '',
                //           score: '',
                //         },
                //         {
                //           type: "3",
                //           num: '',
                //           score: '',
                //         },
                //       ],
                //       courseId: '',
                //       questionsNum: []
                //     },
                //   ]
                this.$refs.automatic.getCourseList(this.formInfo.subjectId)
              })
            }
          }
        });
      },
      // 提交课程
      addExam(type) {
        if (this.typeStr == '0') {
          this.$refs.automatic.submit(type)
        } else {
          this.$refs.handMovement.submit(type)
        }
      },
      // 提交自动考试
      submitAuto(obj, scoreInfo, type) {
        console.log(obj, 'obj');
        if (this.formInfo.stuOrOrg == '0' && !this.userList.length) {
          return this.$message.error('请选择考试学员')
        }
        if (this.formInfo.scoringPersonType == '0' && this.formInfo.scoringType == '1' && !this.scorUserList.length) {
          return this.$message.error("请选择评卷人员");
        }
        let params = {
          ...this.formInfo,
          score: scoreInfo.sumScore,
          count: scoreInfo.nums,
          studentIds: this.formInfo.stuOrOrg == '0' ? this.userList.map(i => i.id).join(',') : '',
          scoringStudentIds: this.formInfo.scoringPersonType == '0' && this.formInfo.scoringType == '1' ? this.scorUserList
            .map(i => i.id).join(',') : '',
          examStatus: type, // 0草稿 2完成
          type: this.typeStr // 0自动组卷/1手动组卷
        }
        delete params.timeLine
        params.deptIds = params.deptIds ? params.deptIds.join(',') : '' //解决保存时报错
        params.orgId = this.formInfo.stuOrOrg == '1' ? params.orgId.join(',') : ''
        params.scoringOrgId = this.formInfo.scoringPersonType == '1' && this.formInfo.scoringType == '1' ? params.scoringOrgId.join(',') : ''
        params.scoringPersonType = this.formInfo.scoringType == '0' ?'':this.formInfo.scoringPersonType
        if (this.typeStr == '0') { //自动组卷
          params.auto = obj.questionsList
          params.auto.forEach(i => delete i.questionsNum)
        } else { //手动组卷
          var newList = JSON.parse(JSON.stringify(obj))
          newList.forEach(k => {
            k.options = JSON.stringify(k.options)
           if(k.type==4){
             k.answer = k.options;
           }
            if (k.courseList) {
              delete k.courseList
            }
          })
          params.noAutoQuestions = newList
        }
        if (this.id) {
          params.id = this.id
          this.$api.examPlanEdit(params).then(res => {
            if (res.code == '200') {
              this.$router.go(-1)
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          this.$api.addExamPlan(params).then(res => {
            if (res.code == '200') {
              this.$router.go(-1)
            } else {
              this.$message.error(res.msg)
            }
          })
        }

      },
      // 学员选择弹窗-------------------------------
      showUserDialog() {
        this.peopleFlag = '0'
        this.peopleDialog = true;
        this.$nextTick(() => {
          this.$refs.userDialogRef.userSelectData = this.userList;
        });
      },
      //评卷人弹窗
      showScorDialog() {
        this.peopleFlag = '1'
        this.peopleDialog = true;
        this.$nextTick(() => {
          this.$refs.userDialogRef.userSelectData = this.scorUserList;
        });
      },
      //关闭弹窗
      closeDialog() {
        this.peopleDialog = false
      },
      sureDialogUser(list) {
        this.peopleDialog = false;
        this.userList = list
        const userNewList = this.userList.map(item => item.name)
        this.userNewList = userNewList
      },
      sureDialogScoring(list) {
        this.peopleDialog = false;
        this.scorUserList = list
        console.log(this.scorUserList);
        
        const scoringList = this.scorUserList.map(item => item.name)
        this.scoringList = scoringList
      },
      deleteTag(index) {
        this.userNewList.splice(index, 1);
        this.userList.splice(index, 1);

      },
      deleteScor(index) {
        this.scorUserList.splice(index, 1);
        this.scoringList.splice(index, 1);
      },
      onCancel() {
        this.$router.go(-1)
      }
    },
  };

</script>
<style lang="scss" scoped>
  .table-content {
    background: #fff;
    border-radius: 4px;
    height: calc(100% - 0px);
  }

  .topFilter {
    padding: 15px;
    height: 60px;
    background-color: #fff;

    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }

  .courseContent {
    height: calc(100% - 60px);
    padding: 0 16px;

    .courseTab {
      height: 40px;
      background-color: #f6f5fa;
      font-size: 14px;
      text-align: center;
      line-height: 40px;
      display: flex;

      .left {
        flex: 1;
        height: 40px;
        box-sizing: border-box;
      }

      .activeLeft {
        color: #fff;
        background: #3562db;
      }

      .centerLeft {
        width: 0;
        height: 0;
        border-left: 20px solid #3562db;
        border-top: 20px solid transparent;
        border-bottom: 20px solid transparent;
      }

      .centerRight {
        width: 0;
        height: 0;
        border-left: 20px solid transparent;
        border-right: 0 solid transparent;
        border-bottom: 20px solid #3562db;
        border-top: 20px solid #3562db;
        margin-left: -16px;
      }

      .right {
        flex: 1;
        height: 40px;
        background: red($color: #000000);
      }

      .activeRight {
        color: #fff;
        background: #3562db;
      }
    }

    .courseInfo {
      height: calc(100% - 60px);
    }

    .formInfo {
      height: calc(100% - 100px);
      overflow: auto;
      margin: 0 0 20px 50px;
    }

    .courseList {
      height: calc(100% - 50px);
      // overflow: auto;
      background-color: #f5f5fa;
      margin: 0 -16px;

      .examInfo {
        height: 184px;
        background-color: #fff;
        padding: 16px 24px;
        margin-bottom: 16px;

        .examContent {
          font-size: 14px;
          display: flex;
          flex-wrap: wrap;

          .itemInfo {
            width: 50%;
            margin-top: 16px;

            .title {
              color: #666;
            }

            .value {
              margin-left: 46px;
              color: #333;
            }
          }

          .fraction {
            height: 54px;
            width: 100%;
            margin-top: 16px;
            background-color: #faf9fc;
            padding: 10px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .set_select_width {
    width: 300px;
    min-height: 80px;
    margin-top: -30px;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    display: flex;
    flex-wrap: wrap;

    span {
      height: 20px;
      line-height: 20px;
      background-color: #f4f4f5;
      border-color: #e9e9eb;
      color: #909399;
      padding: 3px 8px;
      font-size: 12px;
      border-radius: 4px;
      margin: 2px 0 2px 6px;
      cursor: pointer;
    }

    p {
      padding-left: 15px;
      color: rgb(191, 196, 204);
      font-size: inherit;
    }
  }

  ::v-deep .el-date-editor .el-range__icon {
    line-height: 20px !important;
  }

  ::v-deep .el-date-editor .el-range__close-icon {
    line-height: 20px !important;
  }

  .iconQuestion {
    margin-right: 10PX;
    font-size: 20px;
  }

  .tooltipDiv {
    font-size: 16px;
    margin-bottom: 8px;
  }

</style>
