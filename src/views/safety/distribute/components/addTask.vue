<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            {{ type == 'add' ? '创建学习任务' : '编辑学习任务' }}
          </span>
        </div>
      </div>
      <div class="courseContent">
        <div class="courseTab">
          <div class="left activeLeft" @click="upStep">
            1.设置学习任务基础信息
          </div>
          <div class="centerLeft"></div>
          <div v-if="active == '1'" class="centerRight"></div>
          <div :class="['right', active == 1 ? 'activeRight' : '']" @click="nextStep">
            2.设置学习任务课程、考试、培训
          </div>
        </div>
        <div class="courseInfo" v-if="active == '0'">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            基础信息
          </div>
          <el-form label-width="160px" :model="formInfo" :rules="rules" ref="formInfo" class="formInfo">
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="formInfo.name" placeholder="请输入任务名称" show-word-limit maxlength="30"
                style="width: 300px"></el-input>
            </el-form-item>
            <el-form-item label="所属科目" prop="subjectId">
              <el-cascader v-model="formInfo.subjectId" clearable class="sino_sdcp_input mr15" style="width: 300px"
                :options="subjectList" :props="props" placeholder="请选择类型" @change="hangdleChange"
                :disabled="type == 'exam'"></el-cascader>
            </el-form-item>
            <el-form-item label="分配方式" prop="stuOrOrg">
              <el-select v-model="formInfo.stuOrOrg" placeholder="请选择分配方式" style="width: 300px" @change="btnType"
                :disabled="type == 'exam'">
                <el-option v-for="item in stuOrOrgList" :key="item.id" :label="item.label" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="考试学员" class="" v-if="formInfo.stuOrOrg == '0'" key="userNewList">
              <span style="color: red; margin-left: -80px">*</span>
              <div class="set_select_width" @click="showUserDialog">
                <template v-if="userNewList.length">
                  <span v-for="(item, index) in userNewList" :key="index">
                    {{ item }}
                    <i class="el-icon-error" @click.stop="deleteTag(index, 'user')"></i>
                  </span>
                </template>
                <p v-else>请选择人员</p>
              </div>
            </el-form-item>
            <el-form-item label="组织" prop="orgId" v-if="formInfo.stuOrOrg == '1'" key="orgId">
              <el-select v-model="formInfo.orgId" placeholder="请选择组织" style="width: 300px" multiple>
                <el-option v-for="item in deptList" :key="item.id" :label="item.teamName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属部门" prop="">
              <el-cascader v-model="formInfo.deptIds" placeholder="请选择课程公开范围" :options="deptList" :props="deptTree"
                :show-all-levels="false" clearable filterable collapse-tags style="width: 300px">
              </el-cascader>
            </el-form-item>
            <!-- <el-form-item label="考试通过自动发放证书" prop="">
              <el-select v-model="formInfo.credentialId" placeholder="请选择完成获取证书" style="width: 300px">
                <el-option v-for="item in certificateList" :key="item.id" :label="item.certificateName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="任务完成时间" prop="timeLine">
              <el-date-picker v-model="formInfo.timeLine" type="daterange" value-format="yyyy-MM-dd"
                :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                style="width: 280px; ">
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div class="distribute_Detail" v-if="active == '1'">
          <div class="detail_heard">
            <div class="detail_title">{{ formInfo.name }}</div>
            <div class="details_content">
              <div class="detail_heard_left">
                <div class="detailsItem">
                  <span class="title">所属科目：</span>
                  <span class="value">{{ subjectNames }}</span>
                </div>
                <div class="detailsItem">
                  <span class="title">任务完成时间: </span>
                  <span class="value">{{ formInfo.startTime }}至{{ formInfo.endTime }}</span>
                </div>
              </div>
              <div class="detail_heard_right">
                <div class="detailxi">
                  学习人员:
                  <div class="detail_user">{{ userNewList.join(',') }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="distribute_content">
            <el-tabs v-model="activeName" :before-leave="tabLeave">
              <el-tab-pane v-for="item in tabList" :key="item.value" :label="item.label"
                :name="item.value"></el-tab-pane>
            </el-tabs>
            <div class="tabs_title">
              <courseTask v-if="activeName == '0'" ref="courseTask" :courseList="courseList" :newSubJectName='newSubJectName'></courseTask>
              <examTask v-if="activeName == '1'" ref="examTask" :type="type" :examType="examType"
                :subjectId="formInfo.subjectId" :examList="examList" :taskScoreInfo="taskScoreInfo" :examInfo="examInfo"
                :examAutoInfo="examAutoInfo" :startTime="formInfo.startTime" :endTime="formInfo.endTime">
              </examTask>
              <trainTask v-if="activeName == '2'" ref="trainTask" :trainTaskList="trainTaskList"
                :startTime="formInfo.startTime" :endTime="formInfo.endTime"></trainTask>
            </div>
          </div>
        </div>
      </div>
      <!-- 学员弹窗 -->
      <people-dialog v-if="peopleDialog" ref="userDialogRef" :peopleDialog="peopleDialog" @closeDialog="closeDialog"
        @sureDialogUser="sureDialogUser"></people-dialog>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button v-if="active == '0'" type="primary" @click="nextStep">下一步</el-button>
      <el-button v-if="active == '1'" type="primary" plain @click="upStep">上一步</el-button>
      <el-button v-if="active == '1'" type="primary" @click="addTask('0')">保存草稿</el-button>
      <el-button v-if="active == '1'" type="primary" @click="addTask('2')">派发任务</el-button>
    </div>
  </PageContainer>
</template>
<script>
import courseTask from "./courseTask.vue";
import trainTask from "./trainTask.vue";
import examTask from "./examTask.vue";
import PeopleDialog from './peopleDialog.vue';
export default {
  components: { courseTask, trainTask, examTask, PeopleDialog },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      to.meta.title = to.query.type == "edit" ? "任务编辑" : "任务详情";
    }
    next();
  },
  data() {
    return {
      activeName: "0",
      active: "0",
      formInfo: {
        name: "",
        subjectId: null,
        stuOrOrg: "",
        orgId: "",
        deptIds: [],
        credentialId: "",
        timeLine: [],
        startTime: '',
        endTime: ''
      },
      subjectNames: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        }
      },
      rules: {
        name: [
          { required: true, message: "请输入任务名称", trigger: "blur" },
          {
            min: 1,
            max: 30,
            message: "长度在 1 到 30 个字符",
            trigger: "blur",
          },
        ],
        stuOrOrg: [{ required: true, message: "请选择分配方式", trigger: "change" }],
        subjectId: [{ required: true, message: "请选择所属科目", trigger: "change" }],
        timeLine: [{ required: true, message: "请选择任务完成时间", trigger: "change" }],
        orgId: [{ required: true, message: "请选择组织", trigger: "change" }]
      },
      certificateList: [],
      subjectList: [],
      subjectAllList: [],
      stuOrOrgList: [
        {
          id: "0",
          label: "人员",
        },
        {
          id: "1",
          label: "组织",
        },
      ],
      props: {
        children: "children",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      userNewList: [],
      deptList: [],
      deptTree: {
        children: "children",
        label: "teamName",
        value: "id",
        multiple: true,
        emitPath: false,
      },
      peopleDialog: false,
      courseList: [],
      newSubJectName:"",
      examList: [],
      examType: '0',
      trainTaskList: [],
      examInfo: {
        name: '',
        duration: '',
        credentialId: '',
        rejectDeal: '1',
        disc: '',
        passScore: ''
      },
      examAutoInfo: {
        questionsList: [
          {
            questionTypes: [
              {
                type: "1",
                num: 0,
                score: 0,
                blankNum: 0
              },
              {
                type: "2",
                num: 0,
                score: 0,
                blankNum: 0
              },
              {
                type: "3",
                num: 0,
                score: 0,
                blankNum: 0
              },
              {
                type: "4",
                num: 0,
                score: 0,
                blankNum: 0
              },
              {
                type: "5",
                num: 0,
                score: 0,
                blankNum: 0
              },
            ],
            courseId: '',
            questionsNum: []
          },
        ],
      },
      taskScoreInfo: {
        nums: 0,
        sumScore: 0
      },
      type: '',
      id: '',
      routeInfo: {},
      userAllList: [],
      examAllList: [],
      trainAllList: [],
      studentIds: '',
      tabList: [
        {
          value: '0',
          label: '课程任务'
        },
        {
          value: '1',
          label: '考试任务'
        },
        {
          value: '2',
          label: '培训任务'
        }
      ]
    };
  },
  watch: {
    'formInfo.timeLine'(val) {
      if (val.length > 0) {
        this.formInfo.startTime = val[0];
        this.formInfo.endTime = val[1];
      } else {
        this.formInfo.startTime = "";
        this.formInfo.endTime = "";
      }
    },
    activeName(val) {
      if (val == '1' && this.examType == '1') {
        this.$nextTick(() => {
          this.$refs.examTask.$refs.automatic.getAllNums()
        })
      }

    }
  },
  async created() {
    this.type = this.$route.query.type
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.init()
    this.userAllList = await this.getTableList()

    this.id = this.$route.query.id;
    this.studentIds = this.$route.query.studentIds || ''
    if (this.$route.query.resitRecordList) {
      this.resitRecordList = JSON.parse(this.$route.query.resitRecordList) || []
    }
    this.formInfo.subjectId = Number(this.$route.query.subjectId) || null
    if (this.formInfo.subjectId) {
      this.hangdleChange(this.formInfo.subjectId)
    }
    if (this.id) {
      this.getDetails()
    }
    if (this.studentIds) {
      this.formInfo.stuOrOrg = '0'
      let studentIdList = this.studentIds.split(',')
      if (studentIdList && studentIdList.length) {
        this.userList = []
        studentIdList.forEach(i => {
          this.userAllList.forEach(k => {
            if (i == k.id) {
              this.userList.push(k)
            }
          })
        })
        const userNewList = this.userList.map(item => item.name)
        this.userNewList = userNewList
      }
    }
    if (this.type == 'exam') {
      this.tabList = [
        {
          value: '1',
          label: '考试任务'
        }
      ]
      this.activeName = '1'
    }
  },
  mounted() {

  },
  methods: {
    init() {
      // 获取科目
      let data = {
        pageNo: 1,
        pageSize: 999,
      };
      this.$api.subjectListAll(data).then((res) => {
        if (res.code == 200) {
          this.subjectAllList = res.data
          this.subjectList = this.$tools.transData(
            res.data,
            "id",
            "parentId",
            "children"
          )
        } else {
          this.$message.error(res.message);
        }
      });
      // 获取组织
      this.$api.getDeptListLaboratory({}).then((res) => {
        this.deptList = this.$tools.transData(
          res.data.list,
          "id",
          "parentId",
          "children"
        );
      });
      // 获取证书模板
      this.$api.certificateList({ pageNo: 1, pageSize: 9999 }).then((res) => {
        if (res.code == 200) {
          this.certificateList = res.data.records;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取详情
    getDetails() {
      this.$api.getTaskInfo({ id: this.id }).then((res) => {
        if (res.code == '200') {
          this.formInfo.name = res.data.editParamVo.name
          this.formInfo.subjectId = parseInt(res.data.editParamVo.subjectId)
          this.formInfo.stuOrOrg = res.data.editParamVo.stuOrOrg
          this.formInfo.orgId = res.data.editParamVo.orgId.split(',') || ''
          this.formInfo.deptIds = res.data.editParamVo.deptIds.split(',') || ''
          this.formInfo.credentialId = res.data.editParamVo.credentialId
          if (res.data.editParamVo.startTime) {
            this.formInfo.timeLine = [res.data.editParamVo.startTime, res.data.editParamVo.endTime]
          }
          // 考试人员回显
          let studentIdList = res.data.editParamVo.studentIds ? res.data.editParamVo.studentIds.split(',') : []
          if (studentIdList && studentIdList.length) {
            this.userList = []
            studentIdList.forEach(i => {
              this.userAllList.forEach(k => {
                if (i == k.id) {
                  this.userList.push(k)
                }
              })
            })
            const userNewList = this.userList.map(item => item.name)
            this.userNewList = userNewList
          }
          // 培训、考试、课程列表
          this.courseList = res.data.editParamVo.courseList
          this.newSubJectName = res.data.subjectName
          this.examList = res.data.editParamVo.examList
          this.trainTaskList = res.data.editParamVo.trainTaskList
          this.examType = res.data.editParamVo.examType
          this.resitRecordList = res.data.editParamVo.resitRecordList || []
          if (res.data.editParamVo.examPlanDto) {
            this.taskScoreInfo.nums = res.data.editParamVo.examPlanDto.count
            this.taskScoreInfo.sumScore = res.data.editParamVo.examPlanDto.score
            this.examInfo.name = res.data.editParamVo.examPlanDto.name
            this.examInfo.duration = res.data.editParamVo.examPlanDto.duration
            this.examInfo.credentialId = res.data.editParamVo.examPlanDto.credentialId
            this.examInfo.rejectDeal = res.data.editParamVo.examPlanDto.rejectDeal
            this.examInfo.disc = res.data.editParamVo.examPlanDto.disc
            this.examInfo.passScore = res.data.editParamVo.examPlanDto.passScore
            this.examAutoInfo.questionsList = res.data.editParamVo.examPlanDto.auto
          }
        }
      });
    },
    // 获取所有人员名称
    async getTableList() {
      let data = {
        currentPage: 1,
        pageSize: 9999,
        controlTeamId: ''
      }
      let response = await this.$api.getControlTeamUserListLaboratory(data)
      if (response.code == '200') {
        return response.data.list
      } else {
        this.$message.error(response.msg)
      }

    },
    tabLeave() {
      if (this.activeName == '0') {
        this.courseList = this.$refs.courseTask.courseTaskList
      } else if (this.activeName == '1') {
        this.examType = this.$refs.examTask.activeName
        if (this.examType == '0') {
          this.examList = this.$refs.examTask.examinatioList
        } else {
          this.examInfo = this.$refs.examTask.formInfo
          this.examAutoInfo = this.$refs.examTask.autoInfo
          this.taskScoreInfo = this.$refs.examTask.scoreInfo
        }
      } else if (this.activeName == '2') {
        this.trainTaskList = this.$refs.trainTask.trainList
      }
    },
    upStep() {
      if (this.activeName == '0') {
        this.courseList = this.$refs.courseTask.courseTaskList
      } else if (this.activeName == '1') {
        this.examType = this.$refs.examTask.activeName
        if (this.examType == '0') {
          this.examList = this.$refs.examTask.examinatioList
        } else {
          this.examInfo = this.$refs.examTask.formInfo
          this.examAutoInfo = this.$refs.examTask.autoInfo
          this.taskScoreInfo = this.$refs.examTask.score
        }
      } else if (this.activeName == '2') {
        this.trainTaskList = this.$refs.trainTask.trainList
      }
      this.active = "0";
    },
    nextStep() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.active = "1";
        }
      });
    },
    hangdleChange(val) {
      console.log(val);
      if (val) {
        this.subjectNames = this.subjectAllList.find(item => item.id == val).free1
      }
    },
    btnType(val) {
      if (val == '0') {
        this.userList = []
        this.userNewList = []
      } else {
        this.formInfo.orgId = []
      }
    },
    // 学员选择弹窗-------------------------------
    showUserDialog() {
      if (this.type == 'exam') {
        return
      }
      this.peopleDialog = true;
      this.$nextTick(() => {
        this.$refs.userDialogRef.userSelectData = this.userList;
      });
    },
    //关闭弹窗
    closeDialog() {
      this.peopleDialog = false
    },
    sureDialogUser() {
      this.peopleDialog = false;
      this.userList = this.$refs.userDialogRef.userSelectData;
      const userNewList = this.userList.map(item => item.name)
      this.userNewList = userNewList
    },
    deleteTag(index) {
      if (this.type == 'exam') {
        return
      }
      this.userNewList.splice(index, 1);
      this.userList.splice(index, 1);
    },
    addTask(type) {
      // type 为草稿还是派发
      if (this.activeName == '0') {
        this.courseList = this.$refs.courseTask.courseTaskList
      } else if (this.activeName == '1') {
        this.examType = this.$refs.examTask.activeName
        if (this.examType == '0') {
          this.examList = this.$refs.examTask.examinatioList
        } else {
          this.examInfo = this.$refs.examTask.formInfo
          this.examAutoInfo = this.$refs.examTask.autoInfo
          this.taskScoreInfo = this.$refs.examTask.scoreInfo
        }
      } else if (this.activeName == '2') {
        this.trainTaskList = this.$refs.trainTask.trainList
      }
      if (this.examType == '1') {
        this.activeName = '1'
        this.$nextTick(() => {
          this.$refs.examTask.getValidate()
          this.$refs.examTask.$refs.automatic.getAllNums()
        })
      }
      if (this.examType == '0') {
        this.examInfo = {}
        this.examAutoInfo = {}
        this.taskScoreInfo = {}
      } else {
        this.examList = []
      }
      let params = {
        ...this.formInfo,
        courseIds: this.courseList && this.courseList.length > 0 ? this.courseList.map(item => item.id).join(',') : '',
        examIds: this.examList && this.examList.length > 0 ? this.examList.map(item => item.id).join(',') : '',
        trainIds: this.trainTaskList && this.trainTaskList.length > 0 ? this.trainTaskList.map(item => item.id).join(',') : '',
        examPlanDto: this.examInfo,
        examType: this.examType,
        learnStatus: type,
        sourceType: 0, //0为正常新增，1为考试不通过派发
      }
      params.orgId = this.formInfo.stuOrOrg == '1' ? params.orgId.join(',') : ''
      params.deptIds = params.deptIds.join(',')
      params.studentIds = this.formInfo.stuOrOrg == '0' ? this.userList.map(item => item.id).join(',') : ''
      delete params.timeLine
      params.examPlanDto.auto = this.examAutoInfo.questionsList || []
      params.examPlanDto.stuOrOrg = this.formInfo.stuOrOrg
      params.examPlanDto.orgId = params.orgId
      params.examPlanDto.studentIds = params.studentIds
      params.examPlanDto.auto.forEach(item => {
        delete item.questionsNum
      });
      params.examPlanDto.score = this.taskScoreInfo.sumScore || '0',
        params.examPlanDto.count = this.taskScoreInfo.nums || ''
      params.sourceType = this.type == 'exam' ? '1' : '0' // 自动组卷0，手动组卷1
      params.resitRecordList = this.type == 'exam' ? this.resitRecordList : [] // 派发传任务id
      params.courseList = this.courseList
      params.examList = this.examList
      params.trainTaskList = this.trainTaskList
      let str = this.type == 'edit' ? 'editTask' : 'addTask'
      if (this.id) {
        params.id = this.id
      }
      console.log(params, 'params');
      this.$api[str](params).then((res) => {
        if (res.code == '200') {
          this.$router.go(-1)
        } else {
          this.$message.error(res.msg)
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
}

.topFilter {
  padding: 15px;
  height: 40px;
  background-color: #fff;

  .backBar {
    color: #333333;
    height: 100%;
    line-height: 100%;
    font-size: 15px;
    font-weight: 500;
  }
}

.distribute_Detail {
  height: 100%;
  background: #fff !important;

  .detail_heard {
    .details_content {
      display: flex;
      margin-top: 16px;

      .detail_heard_left {
        width: 50%;

        .detailsItem {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
        }

        .title {
          width: 140px;
          font-size: 14px;
          color: #666666;
        }

        .value {
          font-size: 14px;
          color: #333333;
        }
      }

      .detail_heard_right {
        width: 50%;
      }
    }
  }
}

.detail_title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin: 10px 0px;
}

.detailxi {
  display: flex;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}

.detail_user {
  width: 90%;
  margin-left: 10px;
  height: 80px;
  overflow-y: auto;
  font-weight: 400;
  color: #333333;
  font-size: 14px;
}

.detail_user::-webkit-scrollbar {
  display: none;
}

//隐藏滚动条
.detail_user::-webkit-scrollbar {
  //滚动条样式
  width: 5px;
  background: rgb(177, 188, 191);
}

.distribute_content {
  height: calc(100% - 160px);
  background-color: #3562db;
  background: #fff !important;
}

.tabs_title {
  margin-top: 15px;
  height: calc(100% - 50px);
}

::v-deep .el-tabs__item {
  color: #666666;
  padding: 0px 30px;
}

.courseContent {
  height: calc(100% - 60px);
  padding: 0 16px;

  .courseTab {
    height: 40px;
    background-color: #f6f5fa;
    font-size: 14px;
    text-align: center;
    line-height: 40px;
    display: flex;

    .left {
      flex: 1;
      height: 40px;
      box-sizing: border-box;
    }

    .activeLeft {
      color: #fff;
      background: #3562db;
    }

    .centerLeft {
      width: 0;
      height: 0;
      border-left: 20px solid #3562db;
      border-top: 20px solid transparent;
      border-bottom: 20px solid transparent;
    }

    .centerRight {
      width: 0;
      height: 0;
      border-left: 20px solid transparent;
      border-right: 0 solid transparent;
      border-bottom: 20px solid #3562db;
      border-top: 20px solid #3562db;
      margin-left: -16px;
    }

    .right {
      flex: 1;
      height: 40px;
      background: red($color: #000000);
    }

    .activeRight {
      color: #fff;
      background: #3562db;
    }
  }

  .courseInfo {
    height: calc(100% - 60px);
  }

  .formInfo {
    height: calc(100% - 100px);
    overflow: auto;
    margin: 0 0 20px 50px;
  }
}

.set_select_width {
  width: 300px;
  min-height: 80px;
  margin-top: -30px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  display: flex;
  flex-wrap: wrap;

  span {
    height: 20px;
    line-height: 20px;
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
    padding: 3px 8px;
    font-size: 12px;
    border-radius: 4px;
    margin: 2px 0 2px 6px;
    cursor: pointer;
  }

  p {
    padding-left: 15px;
    color: rgb(191, 196, 204);
    font-size: inherit;
  }
}
</style>
