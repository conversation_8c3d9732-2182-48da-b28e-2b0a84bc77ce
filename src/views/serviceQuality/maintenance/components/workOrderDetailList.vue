<template>
  <div class="form-detail">
    <div class="reserve-scorll">
      <div v-for="(taskRecordObj, taskIndex) in workOrderDetail.taskRecord" :key="taskRecordObj.id" class="width: 100%">
        <!-- 创建工单 -->
        <div v-if="taskRecordObj.operationCode === '1'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('CZ', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ workOrderDetail.olgTaskManagement.createDate }}</span>
              <i :ref="'CZright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'CZdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width30">
                <span class="li-first-span">联系人：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.callerName }}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">电话：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.sourcesPhone }}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">工号：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.callerJobNum }}</span>
              </li>
            </ul>
            <!-- <div class="show-content"> -->
            <TransitionHeight :ref="'CZ' + taskIndex" :heigtRef="'CZBox' + taskIndex">
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">工单号：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.workNum }}</span>
                </li>
                <li class="width45">
                  <span class="li-first-span">工单类型：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.workTypeName }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">申报来源：</span><span class="li-last-span">{{ workOrderDetail.workSourceName }}</span>
                </li>
                <li class="width45">
                  <span class="li-first-span">紧急程度：</span
                  ><span class="li-last-span" :class="{ 'urgent-text': workOrderDetail.olgTaskManagement.urgencyDegree !== '2' }">{{
                    getUrgencyLabel(workOrderDetail.olgTaskManagement.urgencyDegree)
                  }}</span>
                </li>
              </ul>
              <!-- 任务点 -->
              <ul v-if="workOrderDetail.olgTaskManagement.deviceId && workOrderDetail.olgTaskManagement.deviceId !=''" class="item-row">
                <li class="width45">
                  <span class="li-first-span">任务点：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.taskPointName || '' }}</span>
                </li>
              </ul>
              <!-- 后勤设备工单显示设备信息 -->
              <ul v-if="workOrderDetail.olgTaskManagement.deviceId && workOrderDetail.olgTaskManagement.deviceId !=''" class="item-row">
                <li class="width45">
                  <span class="li-first-span">设备名称：</span><span class="li-last-span">{{ workOrderDetail.taskDetail?.deviceName || '' }}</span>
                </li>
                <li class="width45">
                  <span class="li-first-span">设备编码：</span><span class="li-last-span">{{ workOrderDetail.taskDetail?.deviceNumber || '' }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">申报属性：</span><span class="li-last-span">{{ workOrderDetail.typeSourceName }}</span>
                </li>
                <!-- 呼叫中心 web来电 -->
                <li v-if="workOrderDetail.olgTaskManagement.workSources === '0' || workOrderDetail.olgTaskManagement.workSources === '2'" class="width45">
                  <span class="li-first-span">来电号码：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.needPhone }}</span>
                </li>
              </ul>
              <!-- 0 1 3  呼叫中心 web app -->
              <ul
                v-if="
                  workOrderDetail.olgTaskManagement.workSources === '0' ||
                    workOrderDetail.olgTaskManagement.workSources === '1' ||
                    workOrderDetail.olgTaskManagement.workSources === '3'
                "
                class="item-row"
              >
                <li class="width90">
                  <span class="li-first-span">录音：</span>
                  <div v-show="workOrderDetail.recordName" id="audio-box">
                    <audio controls>
                      <source :src="$tools.imgUrlTranslation(workOrderDetail.audioPath)" />
                      <!-- src="https://sinomis.oss-cn-beijing.aliyuncs.com/ioms/ZKYXYY/question/2021/06/24/mp3/20210624104642208466.mp3?Expires=1649988786&OSSAccessKeyId=LTAIeUfTgrfT5j7Y&Signature=QI12blGWwn1KzARBWOu7TdeD0rI%3D" -->
                    </audio>
                    <!-- <a href="javascript:;" onclick="downLoad(olgTaskManagement.audioPath)" title="下载">下载</a> -->
                  </div>
                  <span></span>
                </li>
              </ul>
              <!-- 1 3 web app -->
              <ul v-if="workOrderDetail.olgTaskManagement.workSources === '1' || workOrderDetail.olgTaskManagement.workSources === '3'" class="item-row">
                <li class="width90">
                  <span class="li-first-span">附件：</span>
                  <!-- listAttUrl -->
                  <p v-show="workOrderDetail.listAttUrl">
                    <span v-for="(img, index) in workOrderDetail.listAttUrl" :key="index">
                      <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="$tools.imgUrlTranslation(img)" :preview-src-list="[$tools.imgUrlTranslation(img)]">
                      </el-image>
                    </span>
                  </p>
                  <span></span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width45">
                  <!-- <span class="li-first-span">服务时间：</span><span class="li-last-span">{{ $tools.dateToStr(workOrderDetail.olgTaskManagement.appointmentDate) || '立刻' }}</span> -->
                  <span class="li-first-span">服务时间：</span><span class="li-last-span">{{ '立刻' }}</span>
                </li>
                <li class="width45">
                  <span class="li-first-span">要求完工时间：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.requireAccomplishDate }}</span>
                </li>
              </ul>
              <div v-if="workOrderDetail.handle === '1'">
                <div v-for="(wx, index) in workOrderDetail.wxDetail" :key="index">
                  <ul class="item-row">
                    <li class="width90">
                      <span class="li-first-span">服务地点：</span>
                      <span class="li-last-span">{{ wx[1] }}</span>
                      <span
                      ><a href="javascript:void(0)" class="recording-ALabel" @click="openTab(1, wx[4], wx[1])"><i class="el-icon-tickets"></i>申报记录</a></span
                      >
                    </li>
                  </ul>
                  <ul class="item-row">
                    <li class="width90">
                      <span class="li-first-span">所属科室：</span>
                      <span class="li-last-span">{{
                        workOrderDetail.olgTaskManagement.sourcesDeptName === 'undefined' ? '' : workOrderDetail.olgTaskManagement.sourcesDeptName
                      }}</span>
                      <!-- <span>
                        <a href="javascript:void(0)"  class="recording-ALabel" @click="openTab(2, workOrderDetail.olgTaskManagement.sourcesDept, workOrderDetail.olgTaskManagement.sourcesDeptName)" >
                          <i class="el-icon-tickets"></i>申报记录
                        </a></span
                      > -->
                    </li>
                  </ul>
                  <ul class="item-row">
                    <li class="width90">
                      <span class="li-first-span">服务事项：</span>
                      <span v-if="wx[2] !== undefined && wx[2] !== '' && wx[3] !== undefined && wx[3] !== '' && wx[5] !== undefined && wx[5] !== ''" class="li-last-span">{{
                        wx[2] + '-' + wx[3] + '-' + wx[5]
                      }}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </TransitionHeight>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">操作人：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
            </ul>
          </div>
        </div>
        <!-- 已受理 -->
        <div v-if="taskRecordObj.operationCode === '2'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('SL', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'SLright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'SLdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">调度员：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
              <li class="width45">
                <span class="li-first-span">职工工号：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.no : '' }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'SL' + taskIndex" :heigtRef="'SLBox' + taskIndex">
              <ul v-if="workOrderDetail.olgTaskManagement.workSources === '1'" class="item-row">
                <!-- APP来电 -->
                <li class="width90">
                  <span class="li-first-span">来电号码：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.needPhone }}</span>
                </li>
              </ul>
              <!-- <ul class="item-row">
                    <li class="width45">
                      <span class="li-first-span">工单号：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.workNum }}</span>
                    </li>
                    <li class="width45">
                      <span class="li-first-span">工单类型：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.workTypeName }}</span>
                    </li>
                  </ul> -->
              <div v-if="workOrderDetail.handle !== '1'">
                <div v-for="(wx, index) in workOrderDetail.wxDetail" :key="index">
                  <ul class="item-row">
                    <li class="width90">
                      <span class="li-first-span">服务地点：</span>
                      <span class="li-last-span">{{ wx[1] }}</span>
                      <span
                      ><a href="javascript:void(0)" class="recording-ALabel" @click="openTab(1, wx[4], wx[1])"> <i class="el-icon-tickets"></i>申报记录 </a></span
                      >
                    </li>
                  </ul>
                  <ul class="item-row">
                    <li class="width90">
                      <span class="li-first-span">所属科室：</span>
                      <span class="li-last-span">{{
                        workOrderDetail.olgTaskManagement.sourcesDeptName === 'undefined' ? '' : workOrderDetail.olgTaskManagement.sourcesDeptName
                      }}</span>
                      <span
                      ><a
                        href="javascript:void(0)"
                        class="recording-ALabel"
                        @click="openTab(2, workOrderDetail.olgTaskManagement.sourcesDept, workOrderDetail.olgTaskManagement.sourcesDeptName)"
                      ><i class="el-icon-tickets"></i>申报记录</a
                      ></span
                      >
                    </li>
                  </ul>
                  <ul class="item-row">
                    <li class="width90">
                      <span class="li-first-span">服务事项：</span>
                      <span v-if="wx[2] !== undefined && wx[2] !== '' && wx[3] !== undefined && wx[3] !== '' && wx[5] !== undefined && wx[5] !== ''" class="li-last-span">{{
                        wx[2] + '-' + wx[3] + '-' + wx[5]
                      }}</span>
                    </li>
                  </ul>
                </div>
              </div>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">服务部门：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.designateDeptName }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90" style="height: 50px; overflow: auto">
                  <span class="li-first-span">申报描述：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.questionDescription }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已派工 -->
        <div v-if="taskRecordObj.operationCode === '3'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('PG', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'PGright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'PGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">服务人员：</span><span class="li-last-span">{{ taskRecordObj.designatePersonName }}</span>
              </li>
              <li class="width45">
                <span class="li-first-span">人员电话：</span><span class="li-last-span">{{ taskRecordObj.designatePersonPhone }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'PG' + taskIndex" :heigtRef="'PGBox' + taskIndex"> </TransitionHeight>
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">操作人：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
              <li class="width45">
                <span v-if="taskRecordObj.approvalControlId" class="li-first-span" style="color: #3562db; cursor: pointer" @click="goApplication(taskRecordObj.approvalControlId)"
                >挂单申请</span
                >
              </li>
            </ul>
          </div>
        </div>
        <!-- 已挂单 -->
        <div v-if="taskRecordObj.operationCode === '4'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('GD', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'GDright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'GDdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">挂单说明：</span><span class="li-last-span">{{ taskRecordObj.disEntryOrdersReason }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'GD' + taskIndex" :heigtRef="'GDBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">解决方案：</span><span class="li-last-span">{{ taskRecordObj.disEntryOrdersSolution }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">预计解决：</span><span class="li-last-span">{{ moment(taskRecordObj.disPlanSolutionTime).format('YYYY-MM-DD') }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已回访 -->
        <div v-if="taskRecordObj.operationCode === '5'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('HF', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'HFright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'HFdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">回访人：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
              <li class="width45">
                <span class="li-first-span">回访时间：</span><span class="li-last-span">{{ taskRecordObj.createDate }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'HF' + taskIndex" :heigtRef="'HFBox' + taskIndex">
              <!-- 判断登录人医院是否是 某个医院才展示 -->
              <!-- <div>
                <ul class="item-row">
                  <li class="width90">
                    <span class="li-first-span">回访电话：</span><span class="li-last-span">{{ taskRecordObj.callbackCusttel }}</span>
                  </li>
                </ul>
                <ul class="item-row">
                  <li class="width90">
                    <span class="li-first-span">回访录音：</span>
                    <div v-show="taskRecordObj.callbackVoiceUrl" id="audio-box">
                      <audio controls>
                        <source :src="taskRecordObj.callbackVoiceUrl" />
                      </audio>
                    </div>
                    <span></span>
                  </li>
                </ul>
              </div> -->
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">回访说明：</span><span class="li-last-span">{{ taskRecordObj.feedbackExplain }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已催单 -->
        <div v-if="taskRecordObj.operationCode === '8'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('DB', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'DBright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'DBdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">催单说明：</span><span class="li-last-span">{{ taskRecordObj.feedbackExplain }}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">催单人：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">催单时间：</span><span class="li-last-span">{{ taskRecordObj.createDate }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'DB' + taskIndex" :heigtRef="'DBBox' + taskIndex"> </TransitionHeight>
          </div>
        </div>
        <!-- 已完工 -->
        <div v-if="taskRecordObj.operationCode === '6'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('WG', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'WGright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'WGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">完工说明：</span><span class="li-last-span">{{ taskRecordObj.disFinishRemark }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'WG' + taskIndex" :heigtRef="'WGBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span" style="width: 150px">耗材实际使用</span>
                  <table v-if="workOrderDetail.actual?.length" class="maint-table" style="table-layout: fixed">
                    <tbody>
                      <tr>
                        <td style="width: 80%">耗材名称</td>
                        <td>耗材数量</td>
                      </tr>
                      <tr v-for="(item, index) in workOrderDetail.actual" :key="index">
                        <td>
                          <div :title="item[1]" class="one-line">{{ item.materialName }}</div>
                        </td>
                        <td>
                          <div :title="item[2]" class="one-line">{{ item.operateCount }}</div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span" style="width: 150px">故障原因和维修方法</span>
                  <table v-if="taskRecordObj.taskMalfunctionList?.length" class="maint-table" style="table-layout: fixed">
                    <tbody>
                      <tr>
                        <td>故障原因</td>
                        <td>维修方法</td>
                      </tr>
                      <tr v-for="(item, index) in taskRecordObj.taskMalfunctionList" :key="index">
                        <td :title="item.reasonName">
                          <div class="one-line">{{ item.reasonName }}</div>
                        </td>
                        <td>
                          <div v-for="(methodListObj, i) in item.methodList" :key="i" class="one-line" :title="methodListObj.methodName">{{ methodListObj.methodName }}</div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </li>
              </ul>
              <ul v-if="true" class="item-row">
                <li class="width90">
                  <span class="li-first-span">总服务费：</span
                  ><span class="li-last-span">{{ taskRecordObj.completePrice === 'undefined' ? '' : taskRecordObj.completePrice }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">工单附件：</span>
                  <!-- disAttachmentUrlList newUrl -->
                  <p v-if="taskRecordObj.disAttachmentUrl && taskRecordObj.disAttachmentUrl != ''">
                    <span v-for="(img, index) in taskRecordObj.disAttachmentUrl.split(',')" :key="index">
                      <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="$tools.imgUrlTranslation(img)" :preview-src-list="[$tools.imgUrlTranslation(img)]">
                      </el-image>
                    </span>
                  </p>
                  <span></span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">满意度评价：</span>
                  <!-- <span class="li-last-span">{{ workOrderDetail.olgTaskManagement.disDegreeNew }}</span> -->
                  <span class="li-last-span">
                    <el-rate v-model="taskRecordObj.disDegreeNew" show-text text-color="#fff" disabled :texts="rateTexts"> </el-rate>
                  </span>
                </li>
              </ul>
            </TransitionHeight>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">操作人：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
            </ul>
          </div>
        </div>
        <!-- 回退 -->
        <div v-if="taskRecordObj.operationCode === '30'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('HT', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'HTright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'HTdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">回退说明：</span><span class="li-last-span">{{ taskRecordObj.rollbackExplain }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'HT' + taskIndex" :heigtRef="'HTBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">回退照片：</span>
                  <p v-if="taskRecordObj.rollbackImage">
                    <span v-for="(img, index) in taskRecordObj.rollbackImage.split(',')" :key="index">
                      <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="$tools.imgUrlTranslation(img)" :preview-src-list="[$tools.imgUrlTranslation(img)]">
                      </el-image>
                    </span>
                    <span></span>
                  </p>
                </li>
              </ul>
            </TransitionHeight>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">操作人：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
            </ul>
          </div>
        </div>
        <!-- 已取消 -->
        <div v-if="taskRecordObj.operationCode === '7'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('QX', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'QXright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'QXdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <!-- getDictLabel cancel_reason -->
                <span class="li-first-span">取消理由：</span><span class="li-last-span">{{ transform(workOrderDetail.olgTaskManagement.cancelReasonId) }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'QX' + taskIndex" :heigtRef="'QXBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">取消说明：</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.cancelExplain }}</span>
                </li>
              </ul>
            </TransitionHeight>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">操作人：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
            </ul>
          </div>
        </div>
        <!-- 已转单 -->
        <div v-if="taskRecordObj.operationCode === '9'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('ZD', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'ZDright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'ZDdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">服务部门：</span><span class="li-last-span">{{ taskRecordObj.designateDeptName }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'ZD' + taskIndex" :heigtRef="'ZDBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">转单说明：</span><span class="li-last-span">{{ taskRecordObj.feedbackExplain }}</span>
                </li>
              </ul>
            </TransitionHeight>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">操作人：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
            </ul>
          </div>
        </div>
        <!-- 已变更 -->
        <div v-if="taskRecordObj.operationCode === '10'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('BG', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'BGright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'BGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">服务部门：</span><span class="li-last-span">{{ taskRecordObj.designateDeptName }}</span>
              </li>
              <li class="width45">
                <span v-if="taskRecordObj.free2" class="li-first-span" style="color: #3562db; cursor: pointer" @click="goHistory(taskRecordObj)">查看历史</span>
              </li>
            </ul>
            <TransitionHeight :ref="'BG' + taskIndex" :heigtRef="'BGBox' + taskIndex">
              <ul v-if="taskRecordObj.localtionName" class="item-row">
                <li class="width90">
                  <span class="li-first-span">服务地点：</span>
                  <span class="li-last-span">{{ taskRecordObj.localtionName }}</span>
                </li>
              </ul>
              <ul v-if="taskRecordObj.sourcesDeptName" class="item-row">
                <li class="width90">
                  <span class="li-first-span">所属科室：</span>
                  <span class="li-last-span">{{ taskRecordObj.sourcesDeptName }}</span>
                </li>
              </ul>
              <ul v-if="taskRecordObj.itemTypeName || taskRecordObj.itemDetailName || taskRecordObj.itemServiceName" class="item-row">
                <li class="width90">
                  <span class="li-first-span">服务事项：</span>
                  <span v-if="taskRecordObj.itemTypeName" class="li-last-span">{{ taskRecordObj.itemTypeName }}</span>
                  <span v-if="taskRecordObj.itemTypeName && taskRecordObj.itemDetailName">-</span>
                  <span v-if="taskRecordObj.itemDetailName" class="li-last-span">{{ taskRecordObj.itemDetailName }}</span>
                  <span v-if="taskRecordObj.itemDetailName && taskRecordObj.itemServiceName">-</span>
                  <span v-if="taskRecordObj.itemServiceName" class="li-last-span">{{ taskRecordObj.itemServiceName }}</span>
                </li>
              </ul>
              <ul v-if="taskRecordObj.designatePersonName" class="item-row">
                <li class="width90">
                  <span class="li-first-span">服务人员：</span><span class="li-last-span">{{ taskRecordObj.designatePersonName }}</span>
                </li>
              </ul>
              <ul v-if="taskRecordObj.callerJobNum" class="item-row">
                <li class="width90">
                  <span class="li-first-span">工号：</span><span class="li-last-span">{{ taskRecordObj.callerJobNum }}</span>
                </li>
              </ul>
              <ul v-if="taskRecordObj.callerName" class="item-row">
                <li class="width90">
                  <span class="li-first-span">联系人：</span><span class="li-last-span">{{ taskRecordObj.callerName }}</span>
                </li>
              </ul>
              <ul v-if="taskRecordObj.sourcesPhone" class="item-row">
                <li class="width90">
                  <span class="li-first-span">电话：</span><span class="li-last-span">{{ taskRecordObj.sourcesPhone }}</span>
                </li>
              </ul>
              <ul v-if="taskRecordObj.urgencyDegree" class="item-row">
                <li class="width90">
                  <span class="li-first-span">紧急程度：</span
                  ><span class="li-last-span">{{ taskRecordObj.urgencyDegree == '2' ? '一般' : taskRecordObj.urgencyDegree == '1' ? '紧急' : '' }}</span>
                </li>
              </ul>
              <ul v-if="taskRecordObj.typeSources" class="item-row">
                <li class="width90">
                  <span class="li-first-span">申报属性：</span
                  ><span class="li-last-span">{{ taskRecordObj.typeSources == '2' ? '外委巡查' : taskRecordObj.typeSources == '1' ? '医务报修' : '' }}</span>
                </li>
              </ul>
              <ul v-if="taskRecordObj.appointmentType != null" class="item-row">
                <li class="width90">
                  <span class="li-first-span">服务时间：</span>
                  <span class="li-last-span">{{ taskRecordObj.appointmentType == '0' ? '立即' : '预约' + taskRecordObj.appointmentDate }}</span>
                </li>
              </ul>
              <ul v-if="taskRecordObj.completePrice && workOrderDetail.olgTaskManagement.flowcode === '5'" class="item-row">
                <li class="width90">
                  <span class="li-first-span">总服务费：</span><span class="li-last-span">{{ taskRecordObj.completePrice }}</span>
                </li>
              </ul>
              <ul v-if="taskRecordObj.questionDescription" class="item-row">
                <li class="width90">
                  <span class="li-first-span">申报描述：</span><span class="li-last-span">{{ taskRecordObj.questionDescription }}</span>
                </li>
              </ul>
            </TransitionHeight>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">操作人：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
            </ul>
          </div>
        </div>
        <!-- 已转派 -->
        <div v-if="taskRecordObj.operationCode === '11'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('ZP', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'ZPright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'ZPdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">服务人员：</span><span class="li-last-span">{{ taskRecordObj.designatePersonName }}</span>
              </li>
              <li class="width45">
                <span class="li-first-span">人员电话：</span><span class="li-last-span">{{ taskRecordObj.designatePersonPhone }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'ZP' + taskIndex" :heigtRef="'ZPBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">服务部门：</span><span class="li-last-span">{{ taskRecordObj.designateDeptName }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">转派说明：</span><span class="li-last-span">{{ taskRecordObj.feedbackExplain }}</span>
                </li>
              </ul>
            </TransitionHeight>
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">操作人：</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
              <li class="width45">
                <span v-if="taskRecordObj.approvalControlId" class="li-first-span" style="color: #3562db; cursor: pointer" @click="goApplication(taskRecordObj.approvalControlId)"
                >挂单申请</span
                >
              </li>
            </ul>
          </div>
        </div>
        <!-- 到达 -->
        <div v-if="taskRecordObj.operationCode === '31'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('DD', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'DDright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'DDdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">到达时间：</span><span class="li-last-span">{{ taskRecordObj.createDate }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'DD' + taskIndex" :heigtRef="'DDBox' + taskIndex">
              <ul v-if="taskRecordObj.arrivalPicPath" class="item-row">
                <li class="width90">
                  <span class="li-first-span">现场图片：</span>
                  <p>
                    <span v-for="(img, index) in taskRecordObj.arrivalPicPath.split(',')" :key="index">
                      <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="$tools.imgUrlTranslation(img)" :preview-src-list="[$tools.imgUrlTranslation(img)]">
                      </el-image>
                    </span>
                  </p>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">拍照时间：</span><span class="li-last-span">{{ taskRecordObj.arrivalTime }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
      </div>
      <!-- 处理 -->
      <div v-if="workOrderDetail.olgTaskManagement.flowcode !== '6' && !workOrderDetail.relevanceWorknumFlag && !rowData.isViewMode" class="reserve-plan">
        <div class="plan-title">
          <div class="color-box"><i class="el-icon-time"></i></div>
          <div class="linear-g">
            <span class="linear-g-span1">处理</span>
            <span>{{ $tools.dateToStr() }}</span>
          </div>
        </div>
        <div class="plan-content plan-content-line">
          <DealBtn ref="dealBtn" :data="workOrderDetail" @closeDialog="handleCloseDialog" @updateFooterBtn="updateFooterBtn" />
        </div>
        <div class="repair-work">
          <!-- <el-checkbox true-label="2" false-label="1" v-if="workOrderDetail.olgTaskManagement.repairWork === '2'" v-model="workOrderDetail.olgTaskManagement.repairWork">返修工单</el-checkbox> -->
          <el-checkbox v-model="workOrderDetail.olgTaskManagement.repairWork" true-label="2" false-label="1" disabled>返修工单</el-checkbox>
        </div>
      </div>
      <!-- 历史记录 -->
      <historyRecord v-if="isHistoryRecord" :visible.sync="isHistoryRecord" :taskRecordObj="taskRecordObjNew" />
      <!-- 挂单申请详情 -->
      <orderApplication v-if="isOrderApplication" :visible.sync="isOrderApplication" :approvalControlId="approvalControlId" />
    </div>
    <!-- 底部关闭按钮 -->
    <div class="detail-footer">
      <el-button v-if="!rowData.isViewMode" type="primary" @click="handleSave">确定</el-button>
      <el-button type="primary" plain @click="handleCloseDialog">关闭</el-button>
    </div>
  </div>
</template>
<script>
import TransitionHeight from './transitionHeight.vue'
import historyRecord from './historyRecord.vue'
import orderApplication from './orderApplication.vue'
import DealBtn from './DealBtn.vue'
import moment from 'moment'
let that
export default {
  name: 'workOrderDetailList',
  components: {
    TransitionHeight,
    DealBtn,
    historyRecord,
    orderApplication
  },
  filters: {
    UDfilter(val) {
      if (val === '0') {
        return '紧急事故'
      } else if (val === '1') {
        return '紧急催促'
      } else {
        return '一般'
      }
    }
    // cancelFilter(val) {
    //   const row = that.cancelReasonOptions.forEach((e) => {
    //     if (row.length && e.value === val) {
    //       this.cancelFilter = e.label
    //     } else {
    //       this.cancelFilter = ''
    //     }
    //   })
    // }
  },
  props: {
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      moment,
      cancelFilter: '',
      rateTexts: ['非常差', '差', '一般', '满意', '非常满意'],
      cancelReasonOptions: [],
      urgencyOptions: [],
      workOrderDetail: {
        olgTaskManagement: {
          repairWork: ''
        }
      },
      footerBtn: false,
      isHistoryRecord: false,
      isOrderApplication: false, // 挂单申请
      taskRecordObjNew: {},
      approvalControlId: ''
    }
  },
  beforeCreate() {
    that = this
  },
  created() {
    const params = this.rowData
    this.getWorkOrderDetail(params)
    this.getPersonnelDictionary() // 获取紧急程度字典
  },
  methods: {
    // 处理确定按钮点击
    handleSave() {
      // 如果用户没选dealBtn里的处理按钮，直接走关闭方法
      if (!this.footerBtn && this.$refs.dealBtn) {
        this.handleCloseDialog()
        return
      }
      // 调用DealBtn组件的saveForm方法
      if (this.$refs.dealBtn) {
        this.$refs.dealBtn.saveForm()
      }
    },
    // 更新底部按钮状态
    updateFooterBtn(val) {
      this.footerBtn = val
    },
    // 获取紧急程度字典标签
    getUrgencyLabel(value) {
      if (!value || !this.urgencyOptions.length) return ''
      const option = this.urgencyOptions.find((item) => item.dictValue === value)
      return option ? option.dictLabel : ''
    },
    // 获取紧急程度字典数据
    getPersonnelDictionary() {
      this.$api.getPersonnelDictionary({ type: '1' }).then((res) => {
        if (res.code == '200') {
          this.urgencyOptions = res.data
        }
      })
    },
    // 添加处理关闭弹窗的方法，将事件向上传递
    handleCloseDialog() {
      this.$emit('close')
    },
    getWorkOrderDetail(params) {
      this.$api.getWorkOrderDetail({ id: params.id, operSource: 'souye' }).then((res) => {
        // console.log('@', res)
        this.workOrderDetail = res
        // 已取消 获取 字典项
        if (res.taskRecord.length && res.taskRecord.some((e) => e.operationCode === '7')) {
          this.getIomsDictList('cancel_reason')
        }
        // 如果是查看模式，自动展开所有节点，否则只展开已受理节点
        if (this.rowData.isViewMode) {
          this.$nextTick(() => {
            this.expandAllNodes()
          })
        } else {
          // 非查看模式下，仅展开已受理节点
          this.$nextTick(() => {
            this.expandAcceptedNodes()
          })
        }
      })
    },
    // 展开所有节点
    expandAllNodes() {
      if (!this.workOrderDetail.taskRecord || !this.workOrderDetail.taskRecord.length) return
      // 延迟执行以确保DOM已完全渲染
      setTimeout(() => {
        // 遍历所有任务记录
        this.workOrderDetail.taskRecord.forEach((taskRecordObj, taskIndex) => {
          // 根据不同的操作代码设置对应的展开标识
          let boxCode = ''
          switch (taskRecordObj.operationCode) {
            case '1':
              boxCode = 'CZ'
              break // 创建工单
            case '2':
              boxCode = 'SL'
              break // 已受理
            case '3':
              boxCode = 'PG'
              break // 已派工
            case '4':
              boxCode = 'GD'
              break // 已挂单
            case '5':
              boxCode = 'HF'
              break // 已回访
            case '6':
              boxCode = 'WG'
              break // 已完工
            case '7':
              boxCode = 'QX'
              break // 已取消
            case '8':
              boxCode = 'DB'
              break // 已催单
            case '9':
              boxCode = 'ZD'
              break // 已转单
            case '10':
              boxCode = 'BG'
              break // 已变更
            case '11':
              boxCode = 'ZP'
              break // 已转派
            case '30':
              boxCode = 'HT'
              break // 回退
            case '31':
              boxCode = 'DD'
              break // 到达
            default:
              return // 其他情况不处理
          }
          // 展开当前节点
          if (
            boxCode &&
            this.$refs[boxCode + 'right' + taskIndex] &&
            this.$refs[boxCode + 'right' + taskIndex][0] &&
            this.$refs[boxCode + 'down' + taskIndex] &&
            this.$refs[boxCode + 'down' + taskIndex][0] &&
            this.$refs[boxCode + taskIndex] &&
            this.$refs[boxCode + taskIndex][0]
          ) {
            // 修改箭头显示状态
            this.$refs[boxCode + 'right' + taskIndex][0].style.display = 'none'
            this.$refs[boxCode + 'down' + taskIndex][0].style.display = 'inline-block'
            // 获取TransitionHeight组件实例
            const transitionComponent = this.$refs[boxCode + taskIndex][0]
            // 设置show为true以展开
            transitionComponent.show = true
            // 计算实际内容高度并设置
            transitionComponent.$el.style.height = 'auto'
            const contentHeight = transitionComponent.$el.scrollHeight
            transitionComponent.$el.style.height = `${contentHeight}px`
          }
        })
      }, 100) // 给予足够的时间让DOM渲染完成
    },
    // 仅展开已受理节点
    expandAcceptedNodes() {
      if (!this.workOrderDetail.taskRecord || !this.workOrderDetail.taskRecord.length) return
      // 延迟执行以确保DOM已完全渲染
      setTimeout(() => {
        // 遍历任务记录，仅展开已受理节点（operationCode为'2'的节点）
        this.workOrderDetail.taskRecord.forEach((taskRecordObj, taskIndex) => {
          // 仅处理已受理节点
          if (taskRecordObj.operationCode === '2') {
            const boxCode = 'SL'
            // 确保DOM元素存在
            if (
              this.$refs[boxCode + 'right' + taskIndex] &&
              this.$refs[boxCode + 'right' + taskIndex][0] &&
              this.$refs[boxCode + 'down' + taskIndex] &&
              this.$refs[boxCode + 'down' + taskIndex][0] &&
              this.$refs[boxCode + taskIndex] &&
              this.$refs[boxCode + taskIndex][0]
            ) {
              // 修改箭头显示状态
              this.$refs[boxCode + 'right' + taskIndex][0].style.display = 'none'
              this.$refs[boxCode + 'down' + taskIndex][0].style.display = 'inline-block'
              // 获取TransitionHeight组件实例
              const transitionComponent = this.$refs[boxCode + taskIndex][0]
              // 设置show为true以展开
              transitionComponent.show = true
              // 计算实际内容高度并设置
              transitionComponent.$el.style.height = 'auto'
              const contentHeight = transitionComponent.$el.scrollHeight
              transitionComponent.$el.style.height = `${contentHeight}px`
            }
          }
        })
      }, 100) // 给予足够的时间让DOM渲染完成
    },
    // 获取字典
    getIomsDictList(type) {
      const params = {
        type: type
      }
      this.$api.getIomsDictList(params).then((res) => {
        this.cancelReasonOptions = res
      })
    },
    transform(val) {
      let arr = this.cancelReasonOptions.find((i) => {
        return i.value == val
      })
      if (arr) {
        return arr.label
      } else {
        return ''
      }
    },
    // 展开关闭事件
    collectEvent(box, i) {
      // console.log(this.$refs[box + 'right' + i][0].style.display)
      if (this.$refs[box + 'right' + i][0].style.display === 'inline-block') {
        this.$refs[box + 'right' + i][0].style.display = 'none'
        this.$refs[box + 'down' + i][0].style.display = 'inline-block'
        this.$refs[box + i][0].show = true
      } else {
        this.$refs[box + 'right' + i][0].style.display = 'inline-block'
        this.$refs[box + 'down' + i][0].style.display = 'none'
        this.$refs[box + i][0].show = false
      }
    },
    /*
     *  综合维修工单科室及地点的历史申报记录
     * type 对应查询类型：1：地点 2：科室
     * code 对应的科室地点code或者地点code
     * name 对应的科室地点name或者地点name
     */
    openTab(type, code, name) {
      let historyDept = '' // eslint-disable-line no-unused-vars
      let historyDeptName = '' // eslint-disable-line no-unused-vars
      let historyLocaltion = '' // eslint-disable-line no-unused-vars
      let historyLocaltionName = '' // eslint-disable-line no-unused-vars
      if (type === 1) {
        historyLocaltion = code
        if (code !== undefined && code !== '' && code !== null) {
          historyLocaltion = code.replace(/\_/g, ',') // eslint-disable-line no-useless-escape
        }
        historyLocaltionName = name
      } else if (type === 2) {
        historyDept = code
        historyDeptName = name
      }
      console.log(historyDept, historyDeptName, historyLocaltion, historyLocaltionName)
      this.$emit('close')

      // 根据当前路由决定跳转路径
      const targetPath = this.$route.path.includes('maintenanceService') ? '/maintenanceService/workOrderTable' : '/workOrder/workOrderTable'

      this.$router.push({
        path: targetPath,
        query: {
          typeName: 'location',
          historyDept: historyDept,
          historyLocaltion: historyLocaltion,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode
        }
      })
    },
    // 点击查看历史
    goHistory(row) {
      if (row.free2) {
        this.taskRecordObjNew = row.free2
        this.isHistoryRecord = true
      }
    },
    // 挂单申请
    goApplication(val) {
      if (val) {
        this.approvalControlId = val
        this.isOrderApplication = true
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.form-detail {
  height: 100%;
  width: 100%;
  padding: 10px 0px;
  padding-left: 12px;
  overflow-y: scroll;
  box-sizing: border-box;
  ul,
  li {
    list-style: none;
    padding-left: 0;
    margin-left: 0;
    -webkit-padding-start: 0;
    -moz-padding-start: 0;
  }
  /* 确保所有嵌套列表也不会有小圆点 */
  ul li::before {
    content: none;
  }
  .urgent-text {
    color: #f56c6c !important;
  }
  .reserve-scorll {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: auto;
    box-sizing: border-box;
    padding-right: 15px;
  }
  .reserve-plan {
    width: 100%;
    position: relative;
    // height: inherit;
    .plan-title {
      display: flex;
      width: 100%;
      height: 30px;
      .color-box {
        height: 1.25rem;
        width: 1.25rem;
        color: #3562db;
        line-height: 1.25rem;
        text-align: center;
        border-radius: 3px;
        font-size: 16px;
        margin: auto;
      }
      .linear-g {
        margin-left: 2px;
        width: calc(100% - 22px);
        height: 100%;
        background: #f6f5fa;
        font-size: 15px;
        line-height: 30px;
        font-family: PingFangSC-Medium;
        color: #3562db;
        border-radius: 6px;
        cursor: pointer;
        .linear-g-span1 {
          width: 60px;
          margin: 0 15px;
          font-weight: 600;
          display: inline-block;
        }
        .title-icon {
          float: right;
          line-height: 30px;
          margin-right: 5px;
        }
      }
    }
    .plan-content {
      width: calc(100% - 33px);
      margin-left: 11px;
      color: #676a6c;
      font-size: 13px;
      .item-row {
        width: 100%;
        display: flex;
        padding: 10px 0 10px 30px;
        box-sizing: border-box;
        margin-bottom: 0;
        list-style: none;
        .width30 {
          width: 30%;
        }
        .width45 {
          width: 45%;
        }
        .width90 {
          width: 90%;
          display: flex;
          align-items: center;
        }
        ::v-deep .el-image__error,
        ::v-deep .el-image__placeholder {
          background: center;
        }
        .li-first-span {
          display: inline-block;
          width: 90px;
          white-space: nowrap;
          font-size: 16px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #676a6c;
        }
        .li-last-span {
          font-size: 16px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 400;
          color: #676a6c;
        }
        .recording-ALabel {
          color: #0379f1;
          font-size: 14px;
          text-decoration: none;
          i {
            margin: 0 3px 0 10px;
          }
        }
        #audio-box {
          display: flex;
        }
        #audio-box > audio {
          width: 260px;
          height: 30px;
        }
        #audio-box > a {
          width: 40px;
          text-align: center;
          background-color: #2cc7c5;
          height: 35px;
          line-height: 35px;
          color: #fff;
          border-radius: 5px;
          margin-left: 10px;
        }
      }
      .show-content {
        width: 100%;
      }
    }
    .plan-content-line {
      border-left: 1px solid #ebecef;
    }
    .plan-content-noline {
      width: calc(100% - 33px);
      margin-left: 11px;
      padding: 20px 0 20px 20px;
      color: #b5bacb;
      font-size: 13px;
    }
    .maint-table {
      width: 60%;
      td {
        padding: 5px 0 5px 10px;
        border: 1px solid #eee;
        height: 25px;
        line-height: 25px;
        display: table-cell;
        vertical-align: middle;
        color: #676a6c;
      }
      tr:first-child {
        background-color: #f5f6fc;
      }
      td:first-child {
        width: 35%;
      }
      .one-line {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .repair-work {
      float: right;
      margin: 15px 10px;
      position: absolute;
      top: 40px;
      right: 10px;
      ::v-deep .el-checkbox__label {
        color: #121f3e;
      }
    }
  }

  ::v-deep .detail-footer {
    padding: 0px;
    padding-top: 14px;
    text-align: right;
    background-color: #fff;
    span {
      font-size: 16px;
    }
    // .el-button {
    //   background-color: #3562db;
    //   border-color: #3562db;
    //   color: #fff;
    //   padding: 8px 20px;
    //   border-radius: 4px;

    //   &:hover {
    //     background-color: #2c50b8;
    //     border-color: #2c50b8;
    //   }
    // }
  }
}
</style>
