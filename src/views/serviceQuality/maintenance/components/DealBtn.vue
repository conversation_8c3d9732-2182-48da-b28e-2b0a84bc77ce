<template>
  <div v-loading="submitLoading" class="footer">
    <div v-if="data.type === '1' || data.type === ''">
      <!-- 未处理 -->
      <div v-if="data.olgTaskManagement.flowcode === '1'">
        <el-button v-if="data.olgTaskManagement.appointmentType != '1'" class="sino-button-sure" style="margin-left: 10px" @click="taskDetailPlaceOrder()">处理</el-button>
        <el-button v-else class="sino-button-sure" style="margin-left: 10px" @click="taskDetailUpdateOrder()">修改</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailCancel()">取消</el-button>
      </div>
      <!-- 已受理 -->
      <div v-if="data.olgTaskManagement.flowcode === '2'">
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailReturnVisit('催单')">催单</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailTransfer()">转派</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailToPerson()">指派人员</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailEntryOrder()">挂单</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailCancel()">取消</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailUpdateOrder()">修改</el-button>
      </div>
      <!-- 已派工 -->
      <div v-if="data.olgTaskManagement.flowcode === '3'">
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailReturnVisit('催单')">催单</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailTransfer()">转派</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailToPerson()">指派人员</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailEntryOrder()">挂单</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailFinishOrder()">完工</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailDoPrint()">打印</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailCancel()">取消</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailUpdateOrder()">修改</el-button>
      </div>
      <!-- 已挂单 -->
      <div v-if="data.olgTaskManagement.flowcode === '4'">
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailReturnVisit('催单')">催单</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailTransfer()">转派</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailToPerson()">指派人员</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailFinishOrder()">完工</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailDoPrint()">打印</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailCancel()">取消</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailUpdateOrder()">修改</el-button>
      </div>
      <!-- 已完工 -->
      <div v-if="data.olgTaskManagement.flowcode === '5'">
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailReturnVisit('回访')">回访</el-button>
        <el-button v-if="data.olgTaskManagement.isAcceptanceCheck != '1'" class="sino-button-sure" style="margin-left: 10px" @click="taskDetailDegree()">评价</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailDoPrint()">打印</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailUpdateOrder()">修改</el-button>
        <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailRetroversion()">回退</el-button>
      </div>
      <!-- 已取消 -->
      <!-- <div v-if="data.olgTaskManagement.flowcode === '6'">
      </div> -->
    </div>
    <div v-if="data.type === '2'">
      <el-button class="sino-button-sure" style="margin-left: 10px" @click="taskDetailReturnVisit('回复')">回复</el-button>
    </div>
    <div class="footer-content">
      <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium">
        <!-- 催单 回复 回访 -->
        <div v-if="taskDetailReturnVisitFlag">
          <div class="formRow">
            <el-form-item :label="returnVisitName + '说明：'" prop="feedbackFlagExplain">
              <el-input
                v-model.trim="formInline.feedbackFlagExplain"
                maxlength="500"
                :placeholder="returnVisitName == '催单' ? '请输入催单说明，字数限制500字以内' : '请输入描述，字数限制500字以内'"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </div>
          <div v-if="data.olgTaskManagement.flowcode === '5'" class="formRow">
            <!-- <el-form-item label="来电号码：" prop="autoCallNum">
              <el-input v-model.trim="formInline.autoCallNum" maxlength="20" placeholder="请输入来电号码" onkeyup="value=value.replace(/[^\d-]/g,'')"></el-input>
            </el-form-item> -->
          </div>
        </div>
        <!-- 取消 -->
        <div v-if="taskDetailCancelFlag">
          <div class="formRow">
            <el-form-item label="取消理由：" prop="cancelReasonId">
              <el-select v-model="formInline.cancelReasonId" placeholder="请选择取消理由">
                <el-option v-for="item in cancelReasonOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="取消说明：" prop="cancelExplain">
              <el-input
                v-model.trim="formInline.cancelExplain"
                maxlength="500"
                placeholder="请输入取消说明，字数限制500字以内"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <!-- 转派 -->
        <div v-if="taskDetailTransferFlag">
          <div class="formRow">
            <el-form-item label="指派班组：" prop="designateDeptCodeTransfer">
              <div class="select-with-button">
                <el-select v-model="formInline.designateDeptCodeTransfer" placeholder="请选择班组" @change="changeDept">
                  <el-option v-for="item in teamsOptions" :key="item.id" :label="item.team_name" :value="item.id + '_' + item.team_name"> </el-option>
                  <el-option key="ddzx" label="调度中心" value="ddzx_调度中心"> </el-option>
                </el-select>
                <el-button type="primary" size="small" @click="openTeamsSelectDialog">选择</el-button>
              </div>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="服务人员：">
              <el-input v-model="formInline.designatePerson" readonly="readonly" placeholder="请选择服务人员" @focus="designPersonTra('turnTo')"></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="消息通知：" prop="disMessageFlag">
              <el-checkbox v-model="formInline.disMessageFlag"></el-checkbox>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="" prop="">
              <table v-if="selectPeopleRow.length" class="maint-table" style="table-layout: fixed">
                <tbody>
                  <tr>
                    <td style="width: 42%">服务人员</td>
                    <td style="width: 42%">人员电话</td>
                    <td style="width: 16%">操作</td>
                  </tr>
                  <tr v-for="(item, index) in selectPeopleRow" :key="index">
                    <td>
                      <div :title="item.member_name" class="one-line">{{ item.member_name }}</div>
                    </td>
                    <td>
                      <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                    </td>
                    <td>
                      <div class="one-line scope-del" @click="peopleDel(item.id)">删除</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="转派说明：" prop="feedbackExplain">
              <el-input
                v-model.trim="formInline.feedbackExplain"
                maxlength="500"
                placeholder="请输入转派说明，字数限制500字以内"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <!-- 指派 -->
        <div v-if="taskDetailToPersonFlag">
          <div class="formRow">
            <el-form-item label="服务人员：" prop="designatePerson">
              <el-input v-model.trim="formInline.designatePerson" readonly="readonly" placeholder="请选择服务人员" @focus="designPersonTra('pointTo')"></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="消息通知：" prop="disMessageFlag">
              <el-checkbox v-model="formInline.disMessageFlag"></el-checkbox>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="" prop="">
              <table v-if="selectPeopleRow.length" class="maint-table" style="table-layout: fixed">
                <tbody>
                  <tr>
                    <td style="width: 42%">服务人员</td>
                    <td style="width: 42%">人员电话</td>
                    <td style="width: 16%">操作</td>
                  </tr>
                  <tr v-for="(item, index) in selectPeopleRow" :key="index">
                    <td>
                      <div :title="item.member_name" class="one-line">{{ item.member_name }}</div>
                    </td>
                    <td>
                      <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                    </td>
                    <td>
                      <div class="one-line scope-del" @click="peopleDel(item.id)">删除</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </el-form-item>
          </div>
        </div>
        <!-- 挂单 -->
        <div v-if="taskDetailEntryOrderFlag">
          <div class="formRow">
            <el-form-item label="挂单说明：" prop="disEntryOrdersReasonCode">
              <el-select v-model="formInline.disEntryOrdersReasonCode" placeholder="请选择挂单说明">
                <el-option v-for="item in EntryOrderOptions" :key="item.id" :label="item.label" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="解决说明：" prop="disEntryOrdersSolution">
              <el-input
                v-model.trim="formInline.disEntryOrdersSolution"
                maxlength="500"
                placeholder="请输入解决说明，字数限制500字以内"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="预计解决：" prop="planSolutionTime">
              <el-date-picker
                v-model="formInline.planSolutionTime"
                value-format="yyyy-MM-dd"
                popper-class="timePicker"
                type="date"
                :picker-options="pickerOptions"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
          </div>
        </div>
        <!-- 修改 -->
        <div v-if="taskDetailUpdateOrderFlag">
          <div v-if="data.isItem === 'Y'" class="formRow">
            <el-form-item label="服务事项：" prop="itemServiceCode">
              <select-tree
                v-model="itemServiceCode"
                style="width: 80%"
                :level="'3'"
                :data="itemTreeData"
                :props="{ label: 'name', children: 'children' }"
                @getName="getItemServiceName"
              ></select-tree>
            </el-form-item>
          </div>
          <!-- 不等于 已完工 -->
          <div v-if="data.olgTaskManagement.flowcode !== '5'" class="formRow">
            <el-form-item label="服务部门：" prop="designateDeptCodeUpdate">
              <el-select v-model="formInline.designateDeptCodeUpdate" placeholder="请选择服务部门">
                <el-option v-for="item in teamsOptions" :key="item.id" :label="item.team_name" :value="item.id + '_' + item.team_name"> </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div v-if="data.olgTaskManagement.flowcode === '5' && data.completePrice === '1'" class="formRow">
            <el-form-item label="总服务费：" prop="completePrice">
              <el-input v-model.trim="formInline.completePrice" maxlength="10" placeholder="请输入总服务费" onkeyup="value=value.replace(/[^\d-]/g,'')"></el-input
              ><span style="margin-left: 15px">元</span>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="申报描述：" prop="questionDescription">
              <el-input
                v-model.trim="formInline.questionDescription"
                maxlength="500"
                placeholder="请输入描述，字数限制500字以内"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <!-- 回退 -->
        <div v-if="taskDetailRetroversionFlag">
          <div class="formRow">
            <el-form-item label="回退说明：" prop="rollbackExplain">
              <el-input
                v-model.trim="formInline.rollbackExplain"
                maxlength="500"
                placeholder="请输入回退说明，字数限制500字以内"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
          <div class="formRow formRow100">
            <el-form-item label="回退照片：" prop="">
              <el-upload
                action=""
                list-type="picture-card"
                :file-list="fileList2"
                multiple
                accept=".png,.jpg,.jpeg,.gif"
                :http-request="httpRequset2"
                :on-remove="handleRemove2"
                :on-change="handleChange2"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
          </div>
        </div>
        <!-- 完工 -->
        <div v-if="taskDetailFinishOrderFlag">
          <div class="formRow">
            <el-form-item label="完工时间：" prop="finishTime">
              <el-input v-show="false" v-model="formInline.finishTime"></el-input><span>{{ $tools.dateToStr() }}</span>
            </el-form-item>
          </div>
          <div v-if="!data.olgTaskManagement.designatePersonName" class="formRow">
            <el-form-item label="服务人员：" prop="designatePerson">
              <el-input v-model.trim="formInline.designatePerson" readonly="readonly" placeholder="请选择服务人员" @focus="designPersonTra('pointTo')"></el-input>
            </el-form-item>
          </div>
          <div v-if="selectPeopleRow.length" class="formRow">
            <el-form-item label="" prop="">
              <table class="maint-table" style="table-layout: fixed">
                <tbody>
                  <tr>
                    <td style="width: 42%">服务人员</td>
                    <td style="width: 42%">人员电话</td>
                    <td style="width: 16%">操作</td>
                  </tr>
                  <tr v-for="(item, index) in selectPeopleRow" :key="index">
                    <td>
                      <div :title="item.member_name" class="one-line">{{ item.member_name }}</div>
                    </td>
                    <td>
                      <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                    </td>
                    <td>
                      <div class="one-line scope-del" @click="peopleDel(item.id)">删除</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="满意度评价：" prop="score">
              <el-rate v-model="formInline.score" show-text text-color="#fff" :texts="rateTexts" @change="handleScoreChange"> </el-rate>
            </el-form-item>
          </div>
          <!-- 当评分乘以满意度分值权重小于等于满意度阈值时显示选择意见 -->
          <div v-if="showScoreAdviceOptions" class="formRow">
            <el-form-item label="选择意见：" prop="scoreAdvice">
              <el-radio-group v-model="formInline.scoreAdvice" class="evaluate-radio-group">
                <el-radio v-for="item in adviceDict" :key="item.typeCode" :label="item.typeCode">{{ item.typeName }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="评价描述：" prop="evaluationExplain">
              <el-input
                v-model.trim="formInline.evaluationExplain"
                maxlength="50"
                placeholder="请输入评价描述，字数限制50字以内"
                type="textarea"
                onkeyup="if(value.length>50)value=value.slice(0,50)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="签名：">
              <div v-if="imgBase" class="signature-preview" @click="goSign">
                <img style="max-width: 200px; max-height: 100px" :src="imgBase" alt="签名" />
              </div>
              <el-button v-else type="primary" @click="goSign">去签名</el-button>
            </el-form-item>
          </div>
          <!-- <div class="formRow">
            <el-form-item label="耗材实际使用：" prop="factMaterialUse" class="fact-materia">
              <div v-for="(factMaterial, ind) in factMaterialList" :key="ind" class="fact-materia-form">
                <div class="fact-materia-first">
                  <span v-if="ind === 0" class="span-plus" @click="spanPlus">+</span>
                  <span v-else class="span-reduce" @click="spanReduce(ind)">-</span> -->
                  <!-- 名称 id 单价 规格 -->
                  <!-- <el-input v-model="formInline.factMaterialUse[ind]" class="fact-materia-input" readonly="readonly" placeholder="请选择耗材" @focus="materialUsed(ind)"></el-input>
                  <el-input v-show="false" v-model="materialId[ind]"></el-input>
                  <el-input v-show="false" v-model="factMaterialPrice[ind]"></el-input>
                  <el-input v-show="false" v-model="specification[ind]"></el-input>
                </div>
                <div v-if="formInline.factMaterialUse[ind]">
                  <span style="margin-left: 15px">数量：</span>
                  <el-input-number v-model="factMaterialNum[ind]" size="mini" :min="1"></el-input-number>
                </div>
              </div>
            </el-form-item>
          </div> -->
          <!-- <div class="formRow">
            <el-form-item label="总服务费：" prop="completePrice">
              <el-input v-model.trim="formInline.completePrice" maxlength="10" placeholder="请输入总服务费" onkeyup="value=value.replace(/[^\d-]/g,'')"></el-input
              ><span style="margin-left: 15px">元</span>
            </el-form-item>
          </div> -->
          <div class="formRow">
            <el-form-item label="故障原因和维修方法：">
              <div>
                <span style="color: #3562db; cursor: pointer" @click="changeMalfunction">请选择</span>
                <table v-if="newReason.length" class="maint-table" style="table-layout: fixed; margin-left: 0">
                  <tbody>
                    <tr>
                      <td>故障原因</td>
                      <td>维修方法</td>
                      <td>操作</td>
                    </tr>
                    <tr v-for="(item, index) in newReason" :key="index">
                      <td :title="item.reasonName">
                        <div class="one-line">{{ item.reasonName }}</div>
                      </td>
                      <td>
                        <div v-for="(methodListObj, i) in item.methodName.split(',')" :key="i" class="one-line" :title="methodListObj">{{ methodListObj }}</div>
                      </td>
                      <td>
                        <div v-for="(methodListObj, i) in item.methodId.split(',')" :key="i" class="one-line scope-del" @click="malDel(methodListObj)">删除</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="完工说明：" prop="finishRemark">
              <el-input
                v-model.trim="formInline.finishRemark"
                maxlength="500"
                placeholder="请输入描述，字数限制500字以内"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
          <div class="formRow formRow100">
            <el-form-item label="工单附件选择上传：" prop="">
              <el-upload
                action=""
                list-type="picture-card"
                :file-list="fileList"
                multiple
                accept=".png,.jpg,.jpeg,.gif"
                :http-request="httpRequset"
                :on-remove="handleRemove"
                :on-change="handleChange"
              >
                <i class="el-icon-plus"></i>
                <div slot="tip" class="el-upload__tip">最多上传9个文件</div>
              </el-upload>
            </el-form-item>
          </div>
          <el-dialog v-dialogDrag :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="" />
          </el-dialog>
        </div>
        <!-- 评价 -->
        <div v-if="taskDetailDegreeFlag">
          <div class="formRow">
            <el-form-item label="满意度评价：" prop="disDegreeNew">
              <el-rate v-model="formInline.disDegreeNew" show-text text-color="#fff" :texts="rateTexts" @change="handleRateChange"> </el-rate>
            </el-form-item>
          </div>
          <!-- 当评分乘以满意度分值权重小于等于满意度阈值时显示选择意见 -->
          <div v-if="showAdviceOptions" class="formRow">
            <el-form-item label="选择意见：" prop="evaluationAdvice">
              <el-radio-group v-model="formInline.evaluationAdvice" class="evaluate-radio-group">
                <el-radio v-for="item in adviceDict" :key="item.typeCode" :label="item.typeCode">{{ item.typeName }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="备注说明：" prop="remark">
              <el-input
                v-model.trim="formInline.remark"
                maxlength="500"
                placeholder="请输入描述，字数限制500字以内"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="评价描述：" prop="evaluationExplain">
              <el-input
                v-model.trim="formInline.evaluationExplain"
                maxlength="50"
                placeholder="请输入评价描述，字数限制50字以内"
                type="textarea"
                onkeyup="if(value.length>50)value=value.slice(0,50)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item>
              <div v-if="imgBase" class="signature-preview" @click="goSign">
                <img style="max-width: 200px; max-height: 100px" :src="imgBase" alt="签名" />
              </div>
              <el-button v-else type="primary" @click="goSign">去签名</el-button>
            </el-form-item>
          </div>
        </div>
        <!-- <div v-if="taskDetailDoPrintFlag">打印</div> -->
      </el-form>
      <div v-if="footerBtn" class="footer-save">
        <!-- <el-button class="sino-button-sure" type="primary" @click="userSaveFn">保 存</el-button> -->
      </div>
    </div>
    <iframe id="printTemplate" ref="iframeDom" hidden name="printTemplate" src="./printTemplate.html" frameborder="0"></iframe>
    <!-- 选择班组人员弹框 -->
    <template v-if="changeTeamsPeopleShow">
      <teamsPeople
        ref="changeTeamsPeople"
        :selectTeamsData="selectTeamsData"
        :changeTeamsPeopleShow="changeTeamsPeopleShow"
        @peopleSure="peopleSure"
        @closeDialog="closePeopleDialog"
      ></teamsPeople>
    </template>
    <!-- 选择耗材弹窗 -->
    <template v-if="changefactMaterialShow">
      <factMaterial
        ref="changefactMaterial"
        :factMaterialData="factMaterialData"
        :changefactMaterialShow="changefactMaterialShow"
        @factMaterialSure="factMaterialSure"
        @closeDialog="closefactMaterialDialog"
      ></factMaterial>
    </template>
    <!-- 选择故障维修弹窗 -->
    <template v-if="changeMalfunctionShow">
      <malfunction
        ref="changemalfunction"
        :malfunctionData="malfunctionData"
        :itemServiceCode="itemServiceCodeToMal"
        :changeMalfunctionShow="changeMalfunctionShow"
        @malfunctionSure="malfunctionSure"
        @closeDialog="closemalfunctionDialog"
      ></malfunction>
    </template>
    <!-- 处理工单 -->
    <template v-if="workOrderDealShow">
      <createdWorkOrder
        :workOrderDealShow.sync="workOrderDealShow"
        :workTypeCode="data.olgTaskManagement.workTypeCode"
        :workTypeName="data.olgTaskManagement.workTypeName"
        :workTypeId="data.olgTaskManagement.id"
        :dealType="isUpdateOperation ? 'update' : 'deal'"
        :shouldInitData="true"
        @workOrderSure="workOrderSure"
        @closeDialog="workOrderDialog"
      ></createdWorkOrder>
    </template>
    <!-- 打印工单区域 start -->
    <!--startprint-->
    <!-- <template v-show="false"> -->
    <div v-show="false">
      打印内容
      <div id="pagec1">
        <table id="barcode_area" cellpadding="0" cellspacing="0" style="page-break-after: always; padding-bottom: 50px">
          <tr>
            <td colspan="2">
              <div id="" align="right" style="margin: 5px">正本</div>
            </td>
          </tr>
          <tr>
            <td colspan="2" align="center">
              <div id="barcode" style="width: 255px; height: 100px"></div>
            </td>
            <!-- <img id="barcode" src=""/> -->
          </tr>
          <tr>
            <td colspan="2" align="center">{{ data.olgTaskManagement.workNum }}</td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 15px; padding: 3px 0">{{ data.olgTaskManagement.workTypeName }}&nbsp;&nbsp;&nbsp;&nbsp;{{ data.kd_time }}</td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 14px; height: 24px">======= 工单信息 =======</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">联系人：</td>
            <td align="left" class="callName" style="width: 70%; font-size: 15px; height: 22px">{{ data.callerName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">工号：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px">{{ data.olgTaskManagement.callerJobNum }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">电话：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px">{{ data.olgTaskManagement.sourcesPhone }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px">紧急程度：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px">{{ data.urgencyDegree }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">服务地点：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px">
              <span v-for="(wx, wxi) in data.wxDetail" :key="wxi">
                {{ wx[1] }}
              </span>
              <!-- <c:forEach items="{{wxDetail }" var="wx" varStatus="i">
                  <c:if test="{{i.index==0 }"> {{wx[1] } </c:if>
                </c:forEach> -->
            </td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">所属科室：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px">{{ data.olgTaskManagement.sourcesDeptName }}</td>
          </tr>
          <tr v-if="data.olgTaskManagement.workTypeCode != '5'">
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">服务事项：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px">
              <span v-for="(wx, wxi) in data.wxDetail" :key="wxi">
                <span v-if="wx[2] !== undefined && wx[2] !== '' && wx[3] !== undefined && wx[3] !== '' && wx[5] != undefined && wx[5] !== ''">{{
                  wx[2] + '-' + wx[3] + '-' + wx[5]
                }}</span>
              </span>
            </td>
          </tr>
          <!-- <c:choose>
              <c:when test="{{olgTaskManagement.workTypeCode != '5' }">
                <tr>
                  <td align="right" style="width: 30%; font-size: 15px; height: 22px">服务事项：</td>
                  <td align="left" style="width: 70%; font-size: 15px; height: 22px">
                    <c:forEach items="{{wxDetail }" var="wx" varStatus="i">
                      <c:if test="{{i.index==0 }">
                        <c:if test="{{wx[2] != undefined && wx[2]!='' && wx[3] != undefined && wx[3]!='' && wx[5] != undefined && wx[5]!='' }"> {{wx[2]}-{{wx[3] }-{{wx[5] } </c:if>
                      </c:if>
                    </c:forEach>
                  </td>
                </tr>
              </c:when>
              <c:otherwise> </c:otherwise>
            </c:choose> -->
          <!-- <%-- 去掉了指派备注说明 --%> -->
          <tr valign="top">
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">申报描述：</td>
            <td id="remarkZb" align="left" style="width: 70%; font-size: 15px; height: 22px; word-wrap: break-word; word-break: break-all">
              {{ data.olgTaskManagement.questionDescription }}
            </td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px">服务部门：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px">{{ data.olgTaskManagement.designateDeptName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px">服务人员：</td>
            <td id="designatePersonPrint" align="left" style="width: 70%; font-size: 12px; height: 22px">{{ data.olgTaskManagement.designatePersonName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px">服务时间：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px">
              <span>
                {{ data.olgTaskManagement.appointmentDate ? $tools.dateToStr(data.olgTaskManagement.appointmentDate) : '立刻' }}
              </span>
              <!-- <c:choose>
                  <c:when test="{{not empty olgTaskManagement.appointmentDate}">
                    <fmt:formatDate value="{{olgTaskManagement.appointmentDate}" pattern="yyyy-MM-dd HH:mm:ss" />
                  </c:when>
                  <c:otherwise> 立刻 </c:otherwise>
                </c:choose> -->
            </td>
          </tr>
          <!--  增加字段实际耗材及空白区域  -->
          <tr>
            <td valign="top" align="right" style="width: 30%; font-size: 12px; height: 22px">实际耗材：</td>
            <td>
              <canvas id="actualConsumable" height="100px" width="100px"></canvas>
            </td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 12px; height: 22px; padding: 8px 0">
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px"></b>&nbsp;&nbsp;非常满意
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px"></b>&nbsp;&nbsp;满意
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px"></b>&nbsp;&nbsp;一般
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px"></b>&nbsp;&nbsp;差
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px"></b>&nbsp;&nbsp;非常差
            </td>
          </tr>
          <tr>
            <td colspan="3" align="center" style="font-size: 12px; height: 22px">
              <p align="left" style="margin-left: 20px">注:请联系人/接收人在空白处签字并确认实际耗材与数量</p>
            </td>
          </tr>
          <br />
          <br />
          <br />
        </table>
      </div>
      <div id="pagec2">
        <table id="barcode_area2" cellpadding="0" cellspacing="0" style="page-break-after: always; padding-bottom: 50px">
          <tr>
            <td colspan="2">
              <div id="" align="right" style="margin: 5px">副本</div>
            </td>
          </tr>
          <tr>
            <td colspan="2" align="center">
              <div id="barcode2" style="width: 255px; height: 100px"></div>
            </td>
          </tr>
          <tr>
            <td colspan="2" align="center">{{ data.olgTaskManagement.workNum }}</td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 15px; padding: 3px 0">{{ data.olgTaskManagement.workTypeName }}&nbsp;&nbsp;&nbsp;&nbsp;{{ data.kd_time }}</td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 14px; height: 24px">======= 工单信息 =======</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">联系人：</td>
            <td align="left" class="callName" style="width: 70%; font-size: 15px; height: 22px">{{ data.callerName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">工号：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px">{{ data.olgTaskManagement.callerJobNum }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">电话：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px">{{ data.olgTaskManagement.sourcesPhone }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px">紧急程度：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px">{{ data.urgencyDegree }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">服务地点：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px">
              <span v-for="(wx, wxi) in data.wxDetail" :key="wxi">
                {{ wx[1] }}
              </span>
              <!-- <c:forEach items="{{wxDetail }" var="wx" varStatus="i">
                  <c:if test="{{i.index==0 }"> {{wx[1] }} </c:if>
                </c:forEach> -->
            </td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">所属科室：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px">{{ data.olgTaskManagement.sourcesDeptName }}</td>
          </tr>
          <tr v-if="data.olgTaskManagement.workTypeCode != '5'">
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">服务事项：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px">
              <span v-for="(wx, wxi) in data.wxDetail" :key="wxi">
                <span v-if="wx[2] !== undefined && wx[2] !== '' && wx[3] !== undefined && wx[3] !== '' && wx[5] != undefined && wx[5] !== ''">{{
                  wx[2] + '-' + wx[3] + '-' + wx[5]
                }}</span>
              </span>
            </td>
          </tr>
          <!-- <c:choose>
              <c:when test="{{olgTaskManagement.workTypeCode != '5' }">
                <tr>
                  <td align="right" style="width: 30%; font-size: 15px; height: 22px">服务事项：</td>
                  <td align="left" style="width: 70%; font-size: 15px; height: 22px">
                    <c:forEach items="{{wxDetail }" var="wx" varStatus="i">
                      <c:if test="{{i.index==0 }">
                        <c:if test="{{wx[2] != undefined && wx[2]!='' && wx[3] != undefined && wx[3]!='' && wx[5] != undefined && wx[5]!='' }"> {{wx[2]}-{{wx[3] }-{{wx[5] } </c:if>
                      </c:if>
                    </c:forEach>
                  </td>
                </tr>
              </c:when>
              <c:otherwise> </c:otherwise>
            </c:choose> -->
          <!-- <%-- 去掉了指派备注说明 --%> -->
          <tr valign="top">
            <td align="right" style="width: 30%; font-size: 15px; height: 22px">申报描述：</td>
            <td id="remarkFb" align="left" style="width: 70%; font-size: 15px; height: 22px; word-wrap: break-word; word-break: break-all">
              {{ data.olgTaskManagement.questionDescription }}
            </td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px">服务部门：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px">{{ data.olgTaskManagement.designateDeptName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px">服务人员：</td>
            <td id="designatePersonPrint" align="left" style="width: 70%; font-size: 12px; height: 22px">{{ data.olgTaskManagement.designatePersonName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px">服务时间：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px">
              <span>
                {{ data.olgTaskManagement.appointmentDate ? $tools.dateToStr(data.olgTaskManagement.appointmentDate) : '立刻' }}
              </span>
              <!-- <c:choose>
                  <c:when test="{{not empty olgTaskManagement.appointmentDate}">
                    <fmt:formatDate value="{{olgTaskManagement.appointmentDate}" pattern="yyyy-MM-dd HH:mm:ss" />
                  </c:when>
                  <c:otherwise> 立刻 </c:otherwise>
                </c:choose> -->
            </td>
          </tr>
          <!--  增加字段实际耗材及空白区域  -->
          <tr>
            <td valign="top" align="right" style="width: 30%; font-size: 12px; height: 22px">实际耗材：</td>
            <td>
              <canvas id="actualConsumable" height="100px" width="100px"></canvas>
            </td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 12px; height: 22px; padding: 8px 0">
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px"></b>&nbsp;&nbsp;非常满意
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px"></b>&nbsp;&nbsp;满意
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px"></b>&nbsp;&nbsp;一般
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px"></b>&nbsp;&nbsp;差
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px"></b>&nbsp;&nbsp;非常差
            </td>
          </tr>
          <tr>
            <td colspan="3" align="center" style="font-size: 12px; height: 22px">
              <p align="left" style="margin-left: 20px">注:请联系人/接收人在空白处签字并确认实际耗材与数量</p>
            </td>
          </tr>
          <br />
          <br />
          <br />
        </table>
      </div>
      <div id="pagec3">
        <div class="table-content">
          <div class="table-title title-1">{{ data.hospitalFullName }}</div>
          <div class="table-title title-2">后勤维修工作任务单</div>
          <div class="repairNum">
            <span>维修单号</span>
            <span>{{ data.olgTaskManagement.workNum }}</span>
          </div>
          <div class="table-up-box">
            <div>
              <span>接报员</span>
              <span>{{ data.olgTaskManagement.createByName ? data.olgTaskManagement.createByName.slice(0, 4) : '' }}</span>
            </div>
            <div>
              <span>任务分派</span>
              <span>{{ data.olgTaskManagement.designateDeptName ? data.olgTaskManagement.designateDeptName.slice(0, 6) : '' }}</span>
            </div>
            <div>
              <span>维修员</span>
              <span>{{ data.olgTaskManagement.designatePersonName ? data.olgTaskManagement.designatePersonName.slice(0, 4) : '' }}</span>
            </div>
            <div>
              <span>派工时间</span>
              <span>{{ data.olgTaskManagement.createDate }}</span>
            </div>
            <div>
              <span>完工时间</span>
              <span>{{ data.olgTaskManagement.wgTime }}</span>
            </div>
          </div>
          <table>
            <tr>
              <td colspan="1" style="max-width: 80px; width: 80px">报修人</td>
              <td colspan="2" style="max-width: 160px; width: 160px">{{ data.olgTaskManagement.callerName }}</td>
              <td colspan="1" style="max-width: 80px; width: 80px">报修工号</td>
              <td colspan="2" style="max-width: 160px; width: 160px">{{ data.olgTaskManagement.createByNo }}</td>
              <td colspan="1" style="max-width: 80px; width: 80px">联系电话</td>
              <td colspan="2" style="max-width: 160px; width: 160px">{{ data.olgTaskManagement.sourcesPhone }}</td>
            </tr>
            <tr>
              <td colspan="1" style="max-width: 80px; width: 80px">报修科室</td>
              <td colspan="2" style="max-width: 160px; width: 160px">{{ data.olgTaskManagement.sourcesDeptName }}</td>
              <td colspan="1" style="max-width: 80px; width: 160px">地 点</td>
              <td colspan="5" style="max-width: 400px; width: 400px">{{ data.olgTaskManagement.localtionNames }}</td>
            </tr>
            <tr>
              <td colspan="1" style="max-width: 80px; width: 80px">故障描述</td>
              <td colspan="6" style="max-width: 480px; width: 480px">{{}}</td>
              <td colspan="1" style="max-width: 80px; width: 80px">维修数量</td>
              <td colspan="1" style="max-width: 80px; width: 80px"></td>
            </tr>
            <tr>
              <td colspan="1" style="max-width: 80px; width: 80px">备注</td>
              <td colspan="8" style="max-width: 640px; width: 640px">{{ data.olgTaskManagement.questionDescription }}</td>
            </tr>
            <tr>
              <td colspan="2" style="max-width: 160px; width: 160px">维修材料记录</td>
              <td colspan="1" style="max-width: 80px; width: 80px">单位</td>
              <td colspan="1" style="max-width: 80px; width: 80px">数量</td>
              <td colspan="1" style="max-width: 80px; width: 80px">单价</td>
              <td colspan="1" style="max-width: 80px; width: 80px">总价</td>
              <td colspan="3" style="max-width: 160px; width: 160px">维修说明</td>
            </tr>
            <tr>
              <td colspan="2">{{ consumablesData[0] ? consumablesData[0].depName : '' }}</td>
              <td colspan="1">{{ consumablesData[0] ? consumablesData[0].specification : '' }}</td>
              <td colspan="1">{{ consumablesData[0] ? consumablesData[0].outNum : '' }}</td>
              <td colspan="1">{{ consumablesData[0] ? consumablesData[0].price : '' }}</td>
              <td colspan="1">{{ consumablesData[0] ? consumablesData[0].totalPrice : '' }}</td>
              <td colspan="3"></td>
            </tr>
            <tr>
              <td colspan="2">{{ consumablesData[1] ? consumablesData[1].depName : '' }}</td>
              <td colspan="1">{{ consumablesData[1] ? consumablesData[1].specification : '' }}</td>
              <td colspan="1">{{ consumablesData[1] ? consumablesData[1].outNum : '' }}</td>
              <td colspan="1">{{ consumablesData[1] ? consumablesData[1].price : '' }}</td>
              <td colspan="1">{{ consumablesData[1] ? consumablesData[1].totalPrice : '' }}</td>
              <td colspan="3"></td>
            </tr>
            <tr>
              <td colspan="2">{{ consumablesData[2] ? consumablesData[2].depName : '' }}</td>
              <td colspan="1">{{ consumablesData[2] ? consumablesData[2].specification : '' }}</td>
              <td colspan="1">{{ consumablesData[2] ? consumablesData[2].outNum : '' }}</td>
              <td colspan="1">{{ consumablesData[2] ? consumablesData[2].price : '' }}</td>
              <td colspan="1">{{ consumablesData[2] ? consumablesData[2].totalPrice : '' }}</td>
              <td colspan="3"></td>
            </tr>
            <tr>
              <td colspan="2">{{ consumablesData[3] ? consumablesData[3].depName : '' }}</td>
              <td colspan="1">{{ consumablesData[3] ? consumablesData[3].specification : '' }}</td>
              <td colspan="1">{{ consumablesData[3] ? consumablesData[3].outNum : '' }}</td>
              <td colspan="1">{{ consumablesData[3] ? consumablesData[3].price : '' }}</td>
              <td colspan="1">{{ consumablesData[3] ? consumablesData[3].totalPrice : '' }}</td>
              <td colspan="3"></td>
            </tr>
            <tr>
              <td colspan="5">
                <input type="checkbox" />很满意&emsp; <input type="checkbox" />满意&emsp; <input type="checkbox" />一般&emsp; <input type="checkbox" />差&emsp;
                <input type="checkbox" />很差&emsp;
              </td>
              <td>科室确认</td>
              <td colspan="3"></td>
            </tr>
          </table>
          <div class="remark">
            备注：因所用材料要纳入科室经济成本核算，请维修人员如实填写。病区确认请认真审核，多余空格请划除。<br />
            &emsp;&emsp;&emsp;第一联一站式后勤服务中心保留，第二联维修班组保留，第三联保修部门保留
          </div>
        </div>
      </div>
    </div>
    <!-- <section ref="print"> -->
    <!-- <div class="no-print">不要打印我</div> -->
    <!-- </section> -->
    <!-- </template> -->
    <!--b2-->
    <!-- 签名组件 -->
    <template v-if="showSign">
      <signature
        :imgSrc="imgBase"
        :width="500"
        :height="300"
        :workNum="data.olgTaskManagement.workNum"
        :sourcesDeptName="data.olgTaskManagement.sourcesDeptName"
        :materials="getMaterialsList()"
        @change="getImg"
        @show="show"
      ></signature>
    </template>
    <!-- 选择班组弹框 -->
    <el-dialog
      v-dialogDrag
      title="选择班组"
      :visible.sync="teamsSelectDialogVisible"
      width="50%"
      :before-close="handleTeamsSelectDialogClose"
      custom-class="model-dialog"
      :modal="false"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="teams-select-dialog-content">
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-form :inline="true" :model="deptSearchForm" size="small">
            <el-form-item label="所属公司">
              <el-select v-model="deptSearchForm.companyId" placeholder="请选择所属公司" clearable filterable>
                <el-option v-for="item in companyOptions" :key="item.id" :label="item.companyName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="班组名称">
              <el-input v-model="deptSearchForm.teamName" placeholder="请输入班组名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchDeptList">查询</el-button>
              <el-button type="primary" plain @click="resetDeptSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <div class="table-area">
          <el-table
            ref="deptTable"
            v-loading="deptLoading"
            :data="deptTableData"
            border
            style="width: 100%"
            height="300"
            @selection-change="handleDeptSelectionChange"
            @select="handleSelectSingle"
          >
            <el-table-column type="selection" width="55" :selectable="() => true" :reserve-selection="false"></el-table-column>
            <el-table-column label="序号" width="80" align="center">
              <template slot-scope="scope">
                {{ (deptPageNo - 1) * deptPageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="teamName" label="班组名称" align="center"></el-table-column>
            <el-table-column prop="companyName" label="所属公司" align="center"></el-table-column>
            <el-table-column prop="describe" label="描述" align="center"></el-table-column>
            <el-table-column prop="num" label="人员数量" width="80" align="center"></el-table-column>
            <el-table-column prop="phone" label="联系电话" width="120" align="center"></el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              :current-page="deptPageNo"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="deptPageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="deptTotalCount"
              @size-change="handleDeptSizeChange"
              @current-change="handleDeptCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleTeamsSelectDialogClose">取 消</el-button>
        <el-button type="primary" @click="confirmTeamsSelect">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import store from '@/store/index'
import $ from 'jquery'
import teamsPeople from './teamsPeople.vue'
import factMaterial from './factMaterial.vue'
import malfunction from './malfunction.vue'
import selectTree from '@/components/selectTree'
import signatureComponent from './signatureComponent.vue'
export default {
  name: 'DealBtn',
  components: {
    teamsPeople,
    factMaterial,
    malfunction,
    selectTree,
    signature: signatureComponent
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    var validatePhoneNumber = (rule, value, callback) => {
      // 这里每一种情况都要callback，不然表单验证会失效
      // const Cellpho = /^[1][3,4,5,7,8][0-9]{9}$/
      var regexpGH = /(\d{4}-)?\d{6,8}/ // 固话正则
      var regexpSJ = /^1[3456789]\d{9}$/ // 手机号正则
      if (value === '') {
        callback()
      } else if (!regexpGH.test(value) && !regexpSJ.test(value)) {
        callback(new Error('电话格式填写错误!'))
      } else {
        callback()
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (value === '' && this.returnVisitName == '催单') {
        callback(new Error('请输入催单说明'))
      } else if (value === '' && this.returnVisitName != '催单') {
        callback(new Error('请输入描述'))
      } else {
        callback()
      }
    }
    return {
      submitLoading: false,
      consumablesData: [],
      isUpdateOperation: false, // 添加标识，表示是否为修改操作
      taskDetailCancelFlag: false,
      taskDetailReturnVisitFlag: false,
      taskDetailTransferFlag: false,
      taskDetailToPersonFlag: false,
      taskDetailEntryOrderFlag: false,
      taskDetailUpdateOrderFlag: false,
      taskDetailRetroversionFlag: false,
      taskDetailFinishOrderFlag: false,
      taskDetailDegreeFlag: false,
      taskDetailDoPrintFlag: false,
      footerBtn: false, // 保存按钮
      returnVisitName: '', // 催单 回复 说明
      dialogImageUrl: '',
      dialogVisible: false,
      fileList: [],
      fileList2: [],
      formInline: {
        feedbackFlagExplain: '',
        autoCallNum: '',
        designateDeptCodeTransfer: '',
        designatePerson: null,
        remark: '',
        disDegreeNew: 0, // 从空字符串改为数字0
        designateDeptCodeUpdate: '',
        completePrice: null,
        questionDescription: '',
        disMessageFlag: true,
        feedbackExplain: '',
        disEntryOrdersReasonCode: '',
        disEntryOrdersSolution: '',
        planSolutionTime: '',
        cancelExplain: '',
        cancelReasonId: '',
        finishTime: '',
        factMaterialUse: [],
        finishRemark: '',
        score: 0, // 从空字符串改为数字0
        scoreAdvice: '', // 完工部分的选择意见
        evaluationExplain: '',
        evaluationAdvice: '' // 评价部分的选择意见
      },
      materialId: [],
      factMaterialPrice: [],
      specification: [],
      factMaterialNum: [],
      designatePersonValTransfer: '', // 选中转派人员
      itemServiceCode: '', // 服务事项id
      itemServiceName: '', // 服务事项name
      rules: {
        // feedbackFlagExplain: [
        //   { required: true, message: '请输入描述', trigger: 'blur' },
        //   { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        // ],
        feedbackFlagExplain: [
          { required: true, validator: validatePass2, trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ],
        designatePerson: [{ required: true, message: '请选择服务人员' }],
        disEntryOrdersReasonCode: [{ required: true, message: '请选择挂单说明', trigger: 'blur' }],
        disEntryOrdersSolution: [{ required: true, message: '请输入解决说明', trigger: 'blur' }],
        finishRemark: [{ required: true, message: '请输入完工说明', trigger: 'blur' }],
        cancelReasonId: [{ required: true, message: '请选择取消理由', trigger: 'blur' }],
        designateDeptCodeUpdate: [{ required: true, message: '请选择服务部门', trigger: 'blur' }],
        autoCallNum: [{ required: false }, { validator: validatePhoneNumber, trigger: 'blur' }]
      },
      changeTeamsPeopleShow: false, // 列表弹窗
      selectTeamsData: {}, // 选中班组的信息
      selectPeopleRow: [], // 选中人员的信息
      changefactMaterialShow: false, // 耗材弹窗
      selectFactIndex: 0, // 选中 耗材表单的角标
      factMaterialData: {},
      changeMalfunctionShow: false, // 选择 故障维修
      itemServiceCodeToMal: '', // 服务事项ok to 故障维修
      reason: [], // 故障原因 维修方法
      newReason: [], // table
      malfunctionData: {},
      workOrderDealShow: false, // 工单弹窗
      teamsOptions: [], // 班组
      rateTexts: ['非常差', '差', '一般', '满意', '非常满意'],
      DeptOptions: [
        {
          id: '1',
          name: '1'
        },
        {
          id: '2',
          name: '2'
        },
        {
          id: '3',
          name: '3'
        },
        {
          id: '4',
          name: '4'
        }
      ],
      cancelReasonOptions: [],
      EntryOrderOptions: [],
      factMaterialList: 0,
      itemTreeData: [],
      fileData: [],
      fileData2: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      uploadingPhotos: false, // 添加照片上传状态标志
      olgWorkPushNew: {}, // 获取配置参数
      showAdviceOptions: false, // 是否显示选择意见
      showScoreAdviceOptions: false, // 完工部分是否显示选择意见
      adviceDict: [], // 意见选项
      showSign: false, // 控制签名弹窗
      imgBase: '', // 存储签名图片的base64数据
      uploadingFiles: false, // 完工部分附件上传状态标志
      teamsSelectDialogVisible: false, // 选择班组弹框
      deptSearchForm: {
        companyId: '',
        teamName: ''
      },
      deptLoading: false,
      deptTableData: [],
      deptPageNo: 1,
      deptPageSize: 10,
      deptTotalCount: 0,
      selectedDepts: [],
      companyOptions: [] // 公司列表选项
    }
  },
  watch: {
    // 监听footerBtn变化
    footerBtn(newVal) {
      // 通知父组件更新footerBtn状态
      this.$emit('updateFooterBtn', newVal)
    }
  },
  mounted() {
    this.getNewSysConfigParam()
    this.$nextTick(() => {
      // this.doPrintOperator()
      this.getTeamsByWorkTypeCode()
    })
    // console.log('yqf1', this.$parent.$parent.$parent.$parent)
  },
  methods: {
    // 滚动到页面底部
    scrollToBottom() {
      this.$nextTick(() => {
        // 查找滚动容器
        const scrollContainer = document.querySelector('.reserve-scorll') || document.querySelector('.form-detail') || document.documentElement

        if (scrollContainer) {
          // 使用平滑滚动到底部
          scrollContainer.scrollTo({
            top: scrollContainer.scrollHeight,
            behavior: 'smooth'
          })
        }
      })
    },
    // 滚动到表单开始位置
    scrollToFormStart() {
      this.$nextTick(() => {
        // 查找滚动容器和表单容器
        const scrollContainer = document.querySelector('.reserve-scorll') || document.querySelector('.form-detail') || document.documentElement
        const formContainer = document.querySelector('.footer-content')

        if (scrollContainer && formContainer) {
          // 获取表单相对于滚动容器的位置
          const containerRect = scrollContainer.getBoundingClientRect()
          const formRect = formContainer.getBoundingClientRect()
          const scrollTop = scrollContainer.scrollTop

          // 计算表单开始位置
          const targetScrollTop = scrollTop + formRect.top - containerRect.top - 20 // 留20px的间距

          // 使用平滑滚动到表单开始位置
          scrollContainer.scrollTo({
            top: Math.max(0, targetScrollTop),
            behavior: 'smooth'
          })
        }
      })
    },
    // 提供给父组件调用的保存方法
    saveForm() {
      this.userSaveFn()
    },
    goSign() {
      this.showSign = true
    },
    // 获取签名图片
    getImg(img, status) {
      this.imgBase = img
      this.showSign = status
    },
    // 控制签名弹窗显示
    show(status) {
      this.showSign = status
    },
    getNewSysConfigParam() {
      this.$api.getNewSysConfigParam().then((res) => {
        if (res.code === '200' && res.data) {
          this.olgWorkPushNew = res.data.olgWorkPushNew
          this.adviceDict = JSON.parse(res.data.olgWorkPushNew.adviceDict).filter((item) => item.isChecked)
          this.adviceDict.push({ typeName: '其他', typeCode: 'other' })
        }
      })
    },
    // 处理
    taskDetailPlaceOrder() {
      this.workOrderDealShow = true
      // this.$router.push({ path: '/workOrderDeal', query: { workTypeCode: '1', dealType: 'deal' } })
    },
    // 取消
    taskDetailCancel() {
      this.resetData()
      this.getIomsDictList('cancel_reason')
      this.taskDetailCancelFlag = true
      this.scrollToBottom()
    },
    // 催单 回访 回复
    taskDetailReturnVisit(type) {
      this.resetData()
      this.returnVisitName = type
      this.taskDetailReturnVisitFlag = true
      this.scrollToBottom()
    },
    // 转派
    taskDetailTransfer() {
      this.resetData()
      this.getTeamsByWorkTypeCode()
      this.taskDetailTransferFlag = true
      this.scrollToBottom()
    },
    // 指派
    taskDetailToPerson() {
      this.resetData()
      this.taskDetailToPersonFlag = true
      this.scrollToBottom()
    },
    // 指派提交
    taskDetailToPersonSave(params) {
      // return console.log(params)
      this.$api.workOrderOperOrder(params).then((res) => {
        this.submitLoading = false
        if (res.success) {
          this.$message({
            message: res.msg,
            type: 'success'
          })
          const idObj = Object.hasOwn(this.$route.query, 'id') ? this.$route.query : { id: this.data.olgTaskManagement.id }
          this.$parent.getWorkOrderDetail(idObj)
          try {
            this.$parent.$parent.$parent.$parent.getList()
          } catch (e) {
            console.log('执行getList()时发生错误:', e)
          }
          this.resetData()
          this.footerBtn = false
          // 添加事件，通知父组件关闭弹窗
          this.$emit('closeDialog')
        } else {
          this.$message({
            message: res.msg,
            type: 'warning'
          })
        }
      })
    },
    handleChange(file, fileList) {
      this.fileList = fileList
    },
    handleChange2(file, fileList) {
      this.fileList2 = fileList
    },
    // 取消 提交
    taskDetailCanceSave(params) {
      this.$api.placeAndCancelOrder(params).then((res) => {
        this.submitLoading = false
        if (res.success) {
          this.$message({
            message: res.msg,
            type: 'success'
          })
          const idObj = Object.hasOwn(this.$route.query, 'id') ? this.$route.query : { id: this.data.olgTaskManagement.id }
          this.$parent.getWorkOrderDetail(idObj)
          try {
            this.$parent.$parent.$parent.$parent.getList()
          } catch (e) {
            console.log('执行getList()时发生错误:', e)
          }
          this.resetData()
          this.footerBtn = false
          // 添加事件，通知父组件关闭弹窗
          this.$emit('closeDialog')
        } else {
          this.$message({
            message: res.msg,
            type: 'warning'
          })
        }
      })
    },
    // 转派 提交
    taskDetailTransferSave(params) {
      this.$api.toTeamsChangeTask(params).then((res) => {
        this.submitLoading = false
        if (res.success) {
          this.$message({
            message: res.msg,
            type: 'success'
          })
          const idObj = Object.hasOwn(this.$route.query, 'id') ? this.$route.query : { id: this.data.olgTaskManagement.id }
          this.$parent.getWorkOrderDetail(idObj)
          try {
            this.$parent.$parent.$parent.$parent.getList()
          } catch (e) {
            console.log('执行getList()时发生错误:', e)
          }
          this.resetData()
          this.footerBtn = false
          // 添加事件，通知父组件关闭弹窗
          this.$emit('closeDialog')
        } else {
          this.$message({
            message: res.msg,
            type: 'warning'
          })
        }
      })
    },
    // 催单 提交
    taskDetailReturnVisitSave(params) {
      params.typeSources = '1'
      this.$api.addFeedback(params).then((res) => {
        this.submitLoading = false
        if (res.success) {
          this.$message({
            message: res.msg,
            type: 'success'
          })
          const idObj = Object.hasOwn(this.$route.query, 'id') ? this.$route.query : { id: this.data.olgTaskManagement.id }
          this.$parent.getWorkOrderDetail(idObj)
          try {
            this.$parent.$parent.$parent.$parent.getList()
          } catch (e) {
            console.log('执行getList()时发生错误:', e)
          }
          this.resetData()
          this.footerBtn = false
          // 添加事件，通知父组件关闭弹窗
          this.$emit('closeDialog')
        } else {
          this.$message({
            message: res.msg,
            type: 'warning'
          })
        }
      })
    },
    // 回退 提交
    taskDetailRetroversionSave(params) {
      console.log(params, 'paramsparamsparams')
      this.$api.appRollbackTask(params).then((res) => {
        this.submitLoading = false
        if (res.success) {
          this.$message({
            message: res.message,
            type: 'success'
          })
          const idObj = Object.hasOwn(this.$route.query, 'id') ? this.$route.query : { id: this.data.olgTaskManagement.id }
          this.$parent.getWorkOrderDetail(idObj)
          try {
            this.$parent.$parent.$parent.$parent.getList()
          } catch (e) {
            console.log('执行getList()时发生错误:', e)
          }
          this.resetData()
          this.footerBtn = false
          // 添加事件，通知父组件关闭弹窗
          this.$emit('closeDialog')
        } else {
          this.$message({
            message: res.message,
            type: 'warning'
          })
        }
      })
    },
    // 修改 提交
    taskDetailUpdateOrderSave(params) {
      this.$api.updateTask(params).then((res) => {
        this.submitLoading = false
        if (res.success) {
          this.$message({
            message: res.msg,
            type: 'success'
          })
          const idObj = Object.hasOwn(this.$route.query, 'id') ? this.$route.query : { id: this.data.olgTaskManagement.id }
          this.$parent.getWorkOrderDetail(idObj)
          try {
            this.$parent.$parent.$parent.$parent.getList()
          } catch (e) {
            console.log('执行getList()时发生错误:', e)
          }
          this.resetData()
          this.footerBtn = false
          // 添加事件，通知父组件关闭弹窗
          this.$emit('closeDialog')
        } else {
          this.$message({
            message: res.msg,
            type: 'warning'
          })
        }
      })
    },
    // 评价 提交
    taskDetailEvaluateSave(params) {
      this.$api.pdEvaluationOk(params).then((res) => {
        this.submitLoading = false
        if (res.success) {
          this.$message({
            message: res.msg,
            type: 'success'
          })
          const idObj = Object.hasOwn(this.$route.query, 'id') ? this.$route.query : { id: this.data.olgTaskManagement.id }
          this.$parent.getWorkOrderDetail(idObj)
          try {
            this.$parent.$parent.$parent.$parent.getList()
          } catch (e) {
            console.log('执行getList()时发生错误:', e)
          }
          this.resetData()
          this.footerBtn = false
          // 添加事件，通知父组件关闭弹窗
          this.$emit('closeDialog')
        } else {
          this.$message({
            message: res.msg,
            type: 'warning'
          })
        }
      })
    },
    // 挂单
    taskDetailEntryOrder() {
      this.resetData()
      this.getIomsDictList('entry_orders_explain')
      this.taskDetailEntryOrderFlag = true
      this.scrollToBottom()
    },
    // 回退
    taskDetailRetroversion() {
      this.resetData()
      this.taskDetailRetroversionFlag = true
      this.scrollToBottom()
    },
    // 修改
    taskDetailUpdateOrder() {
      this.resetData()
      // 设置修改操作标识
      this.isUpdateOperation = true
      // 判断工单状态，如果是已受理、已派工、已挂单状态，则打开新建工单弹框
      if (['1', '2', '3', '4'].includes(this.data.olgTaskManagement.flowcode)) {
        this.workOrderDealShow = true
        return
      }
      // 其他状态（如已完工）仍使用原来的修改表单
      this.getItemTreeData()
      this.getTeamsByWorkTypeCode(false, 'update')
      this.formInline.completePrice = this.data.olgTaskManagement.completePrice
      this.formInline.questionDescription = this.data.olgTaskManagement.questionDescription
      this.formInline.designateDeptCodeUpdate = this.data.olgTaskManagement.designateDeptCode + '_' + this.data.olgTaskManagement.designateDeptName
      this.taskDetailUpdateOrderFlag = true
      this.scrollToBottom()
    },
    // 完工
    taskDetailFinishOrder() {
      this.resetData()
      this.taskDetailFinishOrderFlag = true
      this.factMaterialList = 0
      this.formInline.completePrice = this.data.olgTaskManagement.completePrice
      this.spanPlus()
      this.scrollToFormStart()
    },
    // 评价
    taskDetailDegree() {
      this.resetData()
      this.taskDetailDegreeFlag = true
      this.scrollToBottom()
    },
    // 打印
    taskDetailDoPrint() {
      // this.resetData()
      // this.taskDetailDoPrintFlag = true
      this.doPrintOperator()
    },
    // 获取班组
    getTeamsByWorkTypeCode(itemCode = false, btnType) {
      let localtionId = this.data.taskDetail?.localtion
      if (btnType === 'update') {
        if (this.data.olgTaskManagement.workTypeCode === '3') {
          localtionId = this.data.taskDetail.transportStartLocal
        } else {
          localtionId = ''
        }
      }
      const params = {
        localtionId: localtionId,
        workTypeCode: this.data.olgTaskManagement.workTypeCode,
        itemTypeCode: itemCode || this.data.taskDetail?.itemTypeCode,
        matterId: itemCode || this.data.taskDetail?.itemTypeCode
      }
      this.$api.getDataByTypeTeam(params).then((res) => {
        // getTeamsByWorkTypeCode(params).then((res) => {
        if (res.code === '200') {
          this.teamsOptions = res.data.list
        }
      })
    },
    // 服务事项返回数据
    getItemServiceName(item) {
      this.itemServiceCode = item.id
      this.itemServiceName = item.name
      this.formInline.designateDeptCodeUpdate = ''
      const itemCode = this.itemServiceCode.split('_')[2]
      this.getTeamsByWorkTypeCode(itemCode, 'update')
      // console.log(item)
    },
    // 获取服务事项
    getItemTreeData() {
      const params = {
        workTypeCode: this.data.olgTaskManagement.workTypeCode
      }
      this.$api.getItemTreeData(params).then((res) => {
        // console.log(res)
        this.itemTreeData = this.$tools.listToTree(res, 'id', 'parent')
      })
    },
    // 获取字典
    getIomsDictList(type) {
      const params = {
        type: type
      }
      this.$api.getIomsDictList(params).then((res) => {
        // console.log(res)
        if (type === 'cancel_reason') {
          this.cancelReasonOptions = res
        } else if (type === 'entry_orders_explain') {
          this.EntryOrderOptions = res
        }
      })
    },
    // 改变班组 人员置空
    changeDept() {
      this.formInline.designatePersonValTransfer = ''
      this.formInline.designatePerson = ''
      this.selectPeopleRow = []
    },
    // 选择人员 打开人员选择弹窗
    designPersonTra(type) {
      if (type === 'turnTo') {
        // console.log(this.formInline.designateDeptCodeTransfer)
        if (!this.formInline.designateDeptCodeTransfer) {
          this.$message({
            message: '请选择班组',
            type: 'warning'
          })
          return
        }
        this.selectTeamsData = {
          deptName: this.formInline.designateDeptCodeTransfer.split('_')[1],
          id: this.formInline.designateDeptCodeTransfer.split('_')[0],
          filterData: true
        }
      } else if (type === 'pointTo') {
        this.selectTeamsData = {
          deptName: this.data.olgTaskManagement.designateDeptName,
          id: this.data.olgTaskManagement.designateDeptCode,
          filterData: true
        }
      }
      this.changeTeamsPeopleShow = true
    },
    // 选择耗材 打开耗材选择弹窗
    materialUsed(index) {
      this.changefactMaterialShow = true
      this.selectFactIndex = index
      // this.formInline.factMaterialUse[index] = index.toString()
      this.$forceUpdate()
    },
    // 选择 故障原因 维修方法 打开 弹窗
    changeMalfunction() {
      this.itemServiceCodeToMal = this.data.wxDetail[0][8]
      this.changeMalfunctionShow = true
    },
    closeDialog() {
      this.resetData()
      this.footerBtn = false
    },
    userSaveFn() {
      const userInfo = store.state.user.userInfo.user
      const params = {
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        officeName: '',
        disFlowcode: this.data.olgTaskManagement.disFlowcode,
        id: this.data.olgTaskManagement.id,
        operSource: this.data.operSource,
        print: this.data.print,
        workTypeCode: this.data.olgTaskManagement.workTypeCode,
        flowcode: this.data.olgTaskManagement.flowcode,
        workNum: this.data.olgTaskManagement.workNum
      }
      // 转派特殊处理，不需要验证服务人员
      if (this.taskDetailTransferFlag) {
        // 构建转派参数
        Object.assign(params, {
          designatePerson: this.formInline.designatePerson,
          designateDeptCodeTransfer: this.formInline.designateDeptCodeTransfer,
          designatePersonValTransfer: this.designatePersonValTransfer,
          disMessageFlag: this.formInline.disMessageFlag ? 'on' : 'off',
          feedbackExplain: this.formInline.feedbackExplain
        })
        // 验证其他必填字段，不包括服务人员
        if (!this.formInline.designateDeptCodeTransfer) {
          return this.$message.warning('请选择班组')
        }
        this.submitLoading = true
        this.taskDetailTransferSave(params)
        return
      }
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          // alert('submit!');
          // 转派 - 已移动到上方特殊处理
          this.submitLoading = true
          // 催单 回访 回复
          if (this.taskDetailReturnVisitFlag) {
            Object.assign(params, { isAssignor: true, feedbackFlagExplain: this.formInline.feedbackFlagExplain })
            this.taskDetailReturnVisitSave(params)
          }
          // 指派人员
          if (this.taskDetailToPersonFlag) {
            Object.assign(params, {
              designatePerson: this.formInline.designatePerson,
              designatePersonVal: this.designatePersonValTransfer,
              messageFlag: this.formInline.disMessageFlag ? 'on' : 'off',
              dealType: 'toPerson',
              operFlag: '',
              disFlowcode: '1'
            })
            // 未派工的以重复派工走 如果不是重复派工，则时间轴需要补全已派工时间轴内容，否则新增已派工时间轴
            if (
              this.data.olgTaskManagement.flowcode !== '1' ||
              (this.data.olgTaskManagement.designatePersonCode !== undefined && this.data.olgTaskManagement.designatePersonCode !== '')
            ) {
              Object.assign(params, { isAssignor: true })
            }
            this.taskDetailToPersonSave(params)
            if (this.data.autoPrintSetting === '1') {
              this.doPrintOperator()
            }
          }
          // 挂单
          if (this.taskDetailEntryOrderFlag) {
            Object.assign(params, {
              disEntryOrdersReasonCode: this.formInline.disEntryOrdersReasonCode,
              disEntryOrdersSolution: this.formInline.disEntryOrdersSolution,
              planSolutionTime: this.formInline.planSolutionTime,
              operFlag: '2'
            })
            this.taskDetailToPersonSave(params)
          }
          // 取消
          if (this.taskDetailCancelFlag) {
            Object.assign(params, {
              cancelReasonId: this.formInline.cancelReasonId,
              cancelExplain: this.formInline.cancelExplain,
              operType: 'cancelOrder'
            })
            this.taskDetailCanceSave(params)
          }
          // 修改
          if (this.taskDetailUpdateOrderFlag) {
            Object.assign(params, {
              itemServiceCode: this.itemServiceCode.split('_')[0],
              itemServiceName: this.itemServiceName.split('_')[0],
              detailCode: this.itemServiceCode.split('_')[1],
              detailName: this.itemServiceName.split('_')[1],
              itemCode: this.itemServiceCode.split('_')[2],
              itemName: this.itemServiceName.split('_')[2],
              completePrice: this.formInline.completePrice,
              designateDeptCodeUpdate: this.formInline.designateDeptCodeUpdate,
              questionDescription: this.formInline.questionDescription
            })
            this.taskDetailUpdateOrderSave(params)
          }
          // 回退
          if (this.taskDetailRetroversionFlag) {
            // 检查照片上传状态
            if (this.uploadingPhotos) {
              this.submitLoading = false
              this.$message.warning('照片上传中，请等待上传完成后再提交')
              return false
            }
            if (this.fileList2.length > 0 && this.fileData2.length === 0) {
              this.submitLoading = false
              this.$message.warning('照片尚未上传完成，请等待上传完成后再提交')
              return false
            }
            Object.assign(params, {
              taskId: this.data.olgTaskManagement.id,
              rollbackExplain: this.formInline.rollbackExplain,
              attachmentUrl: this.fileData2.toString()
            })
            this.taskDetailRetroversionSave(params)
          }
          // 完工
          if (this.taskDetailFinishOrderFlag) {
            // 检查附件上传状态
            if (this.uploadingFiles) {
              this.submitLoading = false
              this.$message.warning('附件上传中，请等待上传完成后再提交')
              return false
            }
            if (this.fileList.length > 0 && this.fileData.length === 0) {
              this.submitLoading = false
              this.$message.warning('附件尚未上传完成，请等待上传完成后再提交')
              return false
            }
            // 转换评价意见code为对应的中文name
            let adviceText = ''
            if (this.formInline.scoreAdvice) {
              const selectedAdvice = this.adviceDict.find((item) => item.typeCode === this.formInline.scoreAdvice)
              adviceText = selectedAdvice ? selectedAdvice.typeName : this.formInline.scoreAdvice
            }
            Object.assign(params, {
              finishTime: this.formInline.finishTime,
              factMaterialUse: this.formInline.factMaterialUse.toString(),
              materialId: this.materialId.toString(),
              factMaterialPrice: this.factMaterialPrice.toString(),
              specification: this.specification.toString(),
              factMaterialNum: this.factMaterialNum.toString(),
              operFlag: '1',
              operType: 'operatorFinish',
              finishRemark: this.formInline.finishRemark,
              reason: JSON.stringify(this.reason),
              score: this.formInline.score === 0 ? '' : this.formInline.score, // 未选择时传空字符串
              evaluationAdvice: adviceText, // 添加选择意见
              completePrice: this.formInline.completePrice,
              attachmentUrl: this.fileData.join(','),
              evaluationExplain: this.formInline.evaluationExplain, // 添加评价描述
              imgBase: this.imgBase // 添加签名图片数据
            })
            if (!this.data.olgTaskManagement.designatePersonName) {
              Object.assign(params, {
                designatePersonValFinish: this.designatePersonValTransfer,
                designatePerson: this.formInline.designatePerson
              })
            }
            this.taskDetailToPersonSave(params)
          }
          // 评价
          if (this.taskDetailDegreeFlag) {
            // 转换评价意见code为对应的中文name
            let adviceText = ''
            if (this.formInline.evaluationAdvice) {
              const selectedAdvice = this.adviceDict.find((item) => item.typeCode === this.formInline.evaluationAdvice)
              adviceText = selectedAdvice ? selectedAdvice.typeName : this.formInline.evaluationAdvice
            }
            Object.assign(params, {
              score: this.formInline.disDegreeNew === 0 ? '' : this.formInline.disDegreeNew, // 未选择时传空字符串
              remark: this.formInline.remark,
              evaluationExplain: this.formInline.evaluationExplain,
              evaluationAdvice: adviceText,
              isSubmit: 0,
              imgBase: this.imgBase // 添加签名图片数据
            })
            this.taskDetailEvaluateSave(params)
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
      console.log(params)
    },
    // 重置 data
    resetData() {
      this.isUpdateOperation = false // 重置修改操作标识
      this.taskDetailCancelFlag = false
      this.taskDetailReturnVisitFlag = false
      this.taskDetailTransferFlag = false
      this.taskDetailToPersonFlag = false
      this.taskDetailEntryOrderFlag = false
      this.taskDetailUpdateOrderFlag = false
      this.taskDetailRetroversionFlag = false
      this.taskDetailFinishOrderFlag = false
      this.taskDetailDegreeFlag = false
      this.taskDetailDoPrintFlag = false
      this.footerBtn = true
      // this.$refs.formInline.resetFields()
      this.returnVisitName = '' // 催单 回复 说明
      this.dialogImageUrl = ''
      this.dialogVisible = false
      this.fileList = []
      this.fileList2 = []
      this.formInline = {
        feedbackFlagExplain: '',
        autoCallNum: '',
        designateDeptCodeTransfer: '',
        designatePerson: null,
        remark: '',
        disDegreeNew: 0, // 从空字符串改为数字0
        designateDeptCodeUpdate: '',
        completePrice: null,
        questionDescription: '',
        disMessageFlag: true,
        feedbackExplain: '',
        disEntryOrdersReasonCode: '',
        disEntryOrdersSolution: '',
        planSolutionTime: '',
        cancelExplain: '',
        cancelReasonId: '',
        finishTime: '',
        factMaterialUse: [],
        finishRemark: '',
        score: 0, // 从空字符串改为数字0
        scoreAdvice: '', // 完工部分的选择意见
        evaluationExplain: '',
        evaluationAdvice: '' // 评价部分的选择意见
      }
      this.materialId = []
      this.factMaterialPrice = []
      this.specification = []
      this.factMaterialNum = []
      this.designatePersonValTransfer = ''
      this.itemServiceCode = ''
      this.itemServiceName = ''
      this.changeTeamsPeopleShow = false
      this.selectTeamsData = {}
      this.selectPeopleRow = []
      this.changefactMaterialShow = false
      this.selectFactIndex = 0
      this.factMaterialData = {}
      this.changeMalfunctionShow = false
      this.itemServiceCodeToMal = ''
      this.reason = []
      this.newReason = []
      this.malfunctionData = {}
      this.$refs.formInline.clearValidate()
      this.fileData = []
      this.fileData2 = []
      this.uploadingPhotos = false
      this.uploadingFiles = false
    },
    closePeopleDialog() {
      this.changeTeamsPeopleShow = false
    },
    peopleSure(item) {
      this.changeTeamsPeopleShow = false
      this.selectPeopleRow = item
      // this.formInline.designatePerson = item.designatePerson
      this.setFormPeopleData(this.selectPeopleRow)
    },
    closefactMaterialDialog() {
      this.changefactMaterialShow = false
    },
    factMaterialSure(item) {
      // console.log(item)
      if (this.materialId.includes(item.id)) {
        return this.$message({
          message: '耗材选择重复！',
          type: 'warning'
        })
      }
      const index = this.selectFactIndex
      this.formInline.factMaterialUse[index] = item.depProductName
      this.materialId[index] = item.id
      this.factMaterialPrice[index] = item.price
      this.specification[index] = item.specification
      this.factMaterialNum[index] = 0
      this.changefactMaterialShow = false
    },
    malfunctionSure(item) {
      this.reason = item
      this.newReason = this.obj_merge(item)
      this.changeMalfunctionShow = false
    },
    closemalfunctionDialog() {
      this.changeMalfunctionShow = false
    },
    workOrderSure(item) {
      // console.log(item)
      this.workOrderDealShow = false
      this.isUpdateOperation = false // 重置修改操作标识
      const idObj = Object.hasOwn(this.$route.query, 'id') ? this.$route.query : { id: this.data.olgTaskManagement.id }
      this.$parent.getWorkOrderDetail(idObj)
      try {
        this.$parent.$parent.$parent.$parent.getList()
      } catch (e) {
        console.log('执行getList()时发生错误:', e)
      }
      this.resetData()
      this.footerBtn = false
    },
    workOrderDialog() {
      this.$message({
        message: '已取消',
        type: 'info',
        duration: 800
      })
      this.workOrderDealShow = false
      this.isUpdateOperation = false // 重置修改操作标识
    },
    // 根据id删除故障维修
    malDel(id) {
      const obj = JSON.parse(JSON.stringify(this.reason))
      this.reason = obj.filter((e) => e.methodId !== id)
      this.newReason = this.obj_merge(this.reason)
    },
    // 根据id删除人员
    peopleDel(id) {
      this.selectPeopleRow = this.selectPeopleRow.filter((e) => e.id !== id)
      this.setFormPeopleData(this.selectPeopleRow)
    },
    // 选中人员 提交数据重组
    setFormPeopleData(selection) {
      // console.log(selection)
      if (selection.length) {
        const person = Array.from(selection, ({ member_name }) => member_name) // eslint-disable-line camelcase
        const personSplit = selection.map((e) => {
          return e.id + '_' + e.member_name + '_' + e.phone
        })
        this.formInline.designatePerson = person.toString()
        this.designatePersonValTransfer = personSplit.toString()
      } else {
        this.formInline.designatePerson = ''
        this.designatePersonValTransfer = ''
      }
    },
    // 增加 耗材
    spanPlus() {
      this.formInline.factMaterialUse.push('')
      this.materialId.push('')
      this.factMaterialPrice.push('')
      this.specification.push('')
      this.factMaterialNum.push(0)
      this.factMaterialList++
    },
    spanReduce(ind) {
      if (this.factMaterialList === 1) {
        return false
      }
      this.formInline.factMaterialUse.splice(ind, 1)
      this.materialId.splice(ind, 1)
      this.factMaterialPrice.splice(ind, 1)
      this.specification.splice(ind, 1)
      this.factMaterialNum.splice(ind, 1)
      this.factMaterialList--
    },
    httpRequset(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.uploadingFiles = true // 开始上传
      this.$api
        .uploadFiles(params)
        .then((res) => {
          if (res.code === '200') {
            this.$message({
              message: '上传成功',
              type: 'success'
            })
            this.fileData.push(res.data.picUrl)
          } else {
            this.$message({
              message: res.message,
              type: 'warning'
            })
          }
          this.uploadingFiles = false // 上传完成
        })
        .catch(() => {
          this.uploadingFiles = false // 上传出错时也重置标志
          this.$message({
            message: '上传失败，请重试',
            type: 'error'
          })
        })
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      // 从fileData数组中移除对应的url
      if (file.response && file.response.data && file.response.data.picUrl) {
        const index = this.fileData.indexOf(file.response.data.picUrl)
        if (index !== -1) {
          this.fileData.splice(index, 1)
        }
      }
    },
    httpRequset2(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.uploadingPhotos = true // 开始上传照片
      this.$api
        .uploadFiles(params)
        .then((res) => {
          if (res.code === '200') {
            this.$message({
              message: '上传成功',
              type: 'success'
            })
            this.fileData2.push(res.data.picUrl)
          } else {
            this.$message({
              message: res.message,
              type: 'warning'
            })
          }
          this.uploadingPhotos = false // 上传完成
        })
        .catch(() => {
          this.uploadingPhotos = false // 上传出错也要重置标志
        })
    },
    handleRemove2(file, fileList) {
      this.fileList2 = fileList
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    obj_merge(obj) {
      const oldObj = JSON.parse(JSON.stringify(obj))
      const newObj = []
      oldObj.forEach((ele, index) => {
        if (index === 0) {
          newObj.push(ele)
        } else {
          const i = newObj.findIndex((e) => e.reasonId === ele.reasonId)
          if (i !== -1) {
            newObj[i].methodId = newObj[i].methodId + ',' + ele.methodId
            newObj[i].methodName = newObj[i].methodName + ',' + ele.methodName
          } else {
            newObj.push(ele)
          }
        }
      })
      return newObj
    },
    // 打印
    doPrintOperator() {
      var content = ''
      // var str = document.getElementById('pagec1').innerHTML // 获取需要打印的页面元素 ，page1元素设置样式page-break-after:always，意思是从下一行开始分割。
      // content = content + str
      // str = document.getElementById('pagec2').innerHTML // 获取需要打印的页面元素
      // content = content + str
      var str = document.getElementById('pagec3').innerHTML
      content += str
      var styleCode = `<style>
        * {
      margin: 0;
      padding: 0;
    }
    .table-title {
      text-align: center;
    }
    .table-content {
        padding-top: 24px;
    }
    .title-1 {
        font-size: 30px;
    }
    .title-2 {
        font-size: 24px;
        letter-spacing: 3px;
    }
    .table-up-box {
        display: flex;
        justify-content: center;
    }
    .table-up-box>div {
        margin: 0 5px;
    }
    .remark {
        width: 70%;
        margin: 0 auto;
    }
    .repairNum {
        padding-left: 15vw;
    }
    table,td {
        border: 1px solid #000;
        border-collapse: collapse;
    }
    td {
        padding: 6px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
    }
    tr {
        height: 36px;
    }
    table {
        margin: 0 auto;
        margin-top: 5px;
        table-layout: fixed;
    }
        </style>`
      $('#printTemplate').contents().find('body').html(content)
      $('#printTemplate').contents().find('head').append(styleCode)
      window.frames['printTemplate'].print() // eslint-disable-line dot-notation
    },
    // 处理评分变化，计算是否显示选择意见
    handleRateChange(value) {
      if (this.olgWorkPushNew && this.olgWorkPushNew.satisfactionConfigScore && this.olgWorkPushNew.satisfactionConfigLimit) {
        // 评分乘以满意度分值权重，判断是否小于等于满意度阈值
        const calculatedScore = value * Number(this.olgWorkPushNew.satisfactionConfigScore)
        this.showAdviceOptions = calculatedScore <= Number(this.olgWorkPushNew.satisfactionConfigLimit)
        // 如果不需要显示选择意见，则清空已选意见
        if (!this.showAdviceOptions) {
          this.formInline.evaluationAdvice = ''
        }
      } else {
        this.showAdviceOptions = false
      }
    },
    handleScoreChange(value) {
      if (this.olgWorkPushNew && this.olgWorkPushNew.satisfactionConfigScore && this.olgWorkPushNew.satisfactionConfigLimit) {
        // 评分乘以满意度分值权重，判断是否小于等于满意度阈值
        const calculatedScore = value * Number(this.olgWorkPushNew.satisfactionConfigScore)
        this.showScoreAdviceOptions = calculatedScore <= Number(this.olgWorkPushNew.satisfactionConfigLimit)
        // 如果不需要显示选择意见，则清空已选意见
        if (!this.showScoreAdviceOptions) {
          this.formInline.scoreAdvice = ''
        }
      } else {
        this.showScoreAdviceOptions = false
      }
    },
    getMaterialsList() {
      // 生成耗材列表数据
      const materials = []
      // 从this.data.actual中获取耗材数据
      if (this.data && this.data.actual && Array.isArray(this.data.actual)) {
        this.data.actual.forEach((item) => {
          if (Array.isArray(item) && item.length >= 3 && item[1] && item[2]) {
            // 格式：耗材名称(item[1]) * 数量(item[2])
            materials.push(item[1] + ' * ' + item[2])
          }
        })
      }
      return materials
    },
    openTeamsSelectDialog() {
      this.teamsSelectDialogVisible = true
      this.getCompanyList()
      this.getDeptList()
    },

    // 获取公司列表
    getCompanyList() {
      const params = {
        pageNo: 1,
        pageSize: 999
      }
      this.$api.oneStopApi
        .getCompanyByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.companyOptions = res.data.rows || []
          } else {
            this.$message.error(res.msg || '获取公司列表失败')
          }
        })
        .catch((err) => {
          console.error('获取公司列表失败', err)
          this.$message.error('获取公司列表失败')
        })
    },
    handleTeamsSelectDialogClose() {
      this.teamsSelectDialogVisible = false
      this.selectedDepts = []
    },
    confirmTeamsSelect() {
      if (this.selectedDepts.length === 0) {
        this.$message.warning('请至少选择一个班组')
        return
      }

      // 将选中的班组添加到下拉选项中
      this.selectedDepts.forEach((dept) => {
        // 检查是否已存在该班组
        const existingTeam = this.teamsOptions.find((team) => team.id === dept.id)
        if (!existingTeam) {
          this.teamsOptions.push({
            id: dept.id,
            team_name: dept.teamName
          })
        }
      })

      // 如果只选择了一个班组，自动设置为当前选中的班组
      if (this.selectedDepts.length === 1) {
        const selectedDept = this.selectedDepts[0]
        this.formInline.designateDeptCodeTransfer = selectedDept.id + '_' + selectedDept.teamName
        this.changeDept() // 清空已选人员
      }

      this.teamsSelectDialogVisible = false
      this.$message.success('已添加选中的班组')
    },
    // 获取班组列表
    getDeptList() {
      this.deptLoading = true
      const params = {
        pageSize: this.deptPageSize,
        pageNo: this.deptPageNo
      }

      // 添加查询条件
      if (this.deptSearchForm.companyId) {
        params.companyId = this.deptSearchForm.companyId
      }
      if (this.deptSearchForm.teamName) {
        params.teamName = this.deptSearchForm.teamName
      }

      this.$api
        .getOrderDeptList(params)
        .then((res) => {
          this.deptLoading = false
          if (res.code === '200') {
            this.deptTableData = res.data.rows || []
            this.deptTotalCount = res.data.total || 0
          } else {
            this.$message.error(res.msg || '获取服务部门列表失败')
          }
        })
        .catch((err) => {
          this.deptLoading = false
          console.error('获取服务部门列表失败', err)
          this.$message.error('获取服务部门列表失败')
        })
    },
    // 搜索班组
    searchDeptList() {
      this.deptPageNo = 1
      this.getDeptList()
    },
    // 重置搜索条件
    resetDeptSearch() {
      this.deptSearchForm = {
        companyId: null, // 将空字符串改为null，确保下拉框能正确清空
        teamName: ''
      }
      this.deptPageNo = 1
      this.getDeptList()
    },
    // 处理选择班组变化
    handleDeptSelectionChange(selection) {
      this.selectedDepts = selection
    },

    // 处理单选逻辑
    handleSelectSingle(selection, row) {
      // 清除之前的所有选择
      this.$refs.deptTable.clearSelection()
      // 只选中当前行
      if (selection.length > 0) {
        this.$refs.deptTable.toggleRowSelection(row, true)
        this.selectedDepts = [row]
      }
    },
    // 直接选择某一行班组
    selectDept(row) {
      // 清除之前的所有选择
      this.$refs.deptTable.clearSelection()
      // 选中当前行
      this.$refs.deptTable.toggleRowSelection(row, true)
      this.selectedDepts = [row]
      this.confirmTeamsSelect()
    },
    // 处理分页大小变化
    handleDeptSizeChange(val) {
      this.deptPageNo = 1
      this.deptPageSize = val
      this.getDeptList()
    },
    // 处理页码变化
    handleDeptCurrentChange(val) {
      this.deptPageNo = val
      this.getDeptList()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-button span {
  font-size: 16px;
}
::v-deep .evaluate-radio-group {
  display: flex;
  align-items: center;
}
.signature-preview {
  cursor: pointer;
  border: 1px dashed #d7e0f8;
  display: inline-flex;
  padding: 5px;
  border-radius: 4px;
}
.signature-preview:hover {
  border-color: #3562db;
}
::v-deep .model-dialog {
  margin-top: 15vh !important;
}

.teams-select-dialog-content {
  padding: 10px;
  background-color: #fff;
  width: 100%;
  .table-area {
    .pagination-container {
      margin-top: 15px;
      text-align: right;
    }
  }
}

.footer {
  padding: 10px 20px;
  box-sizing: border-box;
  .sino-button-sure {
    // width: 70px;
    min-width: 70px;
    height: 38px;
  }
  .footer-content {
    padding: 15px 10px;
    box-sizing: border-box;
    .formRow {
      width: 70%;
      display: flex;
      ::v-deep .el-form-item {
        flex: 1;
        display: flex;
        .el-form-item__label {
          font-size: 16px;
          color: #676a6c;
        }
      }
      ::v-deep .el-form-item__content {
        flex: 1;
        display: flex;
        .el-rate {
          margin: auto 0;
        }
        > .el-textarea {
          width: 80%;
        }
        > .el-select,
        > .el-input {
          width: 40%;
        }
        .select-with-button {
          display: flex;
          align-items: center;
          width: 100%;
          .el-select {
            width: 40%;
            margin-right: 10px;
          }
        }
      }
      .fact-materia {
        ::v-deep .el-form-item__content {
          flex-direction: column;
        }
        .fact-materia-form {
          display: flex;
          margin-bottom: 15px;
          > .el-input,
          > div {
            width: 40%;
          }
          .fact-materia-first {
            padding-left: 15px;
            position: relative;
            .span-plus {
              position: absolute;
              top: 0;
              left: -10px;
              font-size: 24px;
              color: #3562db;
              cursor: pointer;
            }
            .span-reduce {
              position: absolute;
              top: 0;
              left: -10px;
              font-size: 32px;
              color: #f00;
              cursor: pointer;
            }
          }
          ::v-deep .el-input-number--mini {
            width: 6rem;
          }
        }
      }
    }
    .formRow100 {
      width: 99%;
    }
    .maint-table {
      width: 70%;
      margin: 0 0 20px 80px;
      td {
        padding: 5px 0 5px 10px;
        border: 1px solid #eee;
        height: 25px;
        line-height: 25px;
        vertical-align: middle;
        color: #676a6c;
      }
      tr:first-child {
        background-color: #f5f6fc;
      }
      td:first-child {
        width: 35%;
      }
      .one-line {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .scope-del {
        color: #3562db;
        cursor: pointer;
      }
    }
    .footer-save {
      width: 90%;
      text-align: right;
    }
  }
}
</style>
