<template>
  <div class="one-stop-service">
    <!-- 测试按钮 -->
    <!-- <el-button type="primary" class="test-btn" @click="simulateNewBacklog">模拟新待办</el-button> -->
    <!-- <el-button type="warning" class="test-timeout-btn" @click="testTimeoutNotification">测试超时通知</el-button> -->
    <!-- <el-button type="success" class="test-new-btn" @click="testNewWorkOrderNotification">测试新工单通知</el-button> -->
    <!-- <el-button type="info" class="test-refresh-btn" @click="testRefreshBacklog">测试刷新待办</el-button> -->
    <div class="service-layout" :class="{ expanded: isLeftExpanded }">
      <!-- 左侧工单列表 -->
      <div class="left-section" :class="{ expanded: isLeftExpanded }">
        <content-card :showTitle="false">
          <div slot="content">
            <!-- 状态筛选按钮 -->
            <div class="status-filter">
              <div class="status-buttons-group">
                <div class="status-button" :class="{ active: currentStatus === '0' }" @click="handleStatusChange('0')">全部 ({{ workOrderListCount.countAllOrder || 0 }})</div>
                <div class="status-button" :class="{ active: currentStatus === '1' }" @click="handleStatusChange('1')">
                  未派工 ({{ workOrderListCount.countNotYetHandle || 0 }})
                </div>
                <div class="status-button" :class="{ active: currentStatus === '2' }" @click="handleStatusChange('2')">
                  已派工 ({{ workOrderListCount.countAlreadyDispatched || 0 }})
                </div>
                <div class="status-button" :class="{ active: currentStatus === '3' }" @click="handleStatusChange('3')">已挂单 ({{ workOrderListCount.countRegister || 0 }})</div>
                <div class="status-button" :class="{ active: currentStatus === '4' }" @click="handleStatusChange('4')">已结束 ({{ workOrderListCount.countCompleted || 0 }})</div>
                <div class="status-button" :class="{ active: currentStatus === '6' }" @click="handleStatusChange('6')">暂存 ({{ workOrderListCount.countTemporary || 0 }})</div>
              </div>
              <div class="right-control">
                <div class="timeout-button" :class="{ active: currentStatus === '5' }" @click="handleTimeoutClick">响应超时 ({{ workOrderListCount.countOverTimes || 0 }})</div>
                <img src="@/assets/images/setting.png" class="setting-icon" alt="设置" @click="handleSettingClick" />
                <div class="expand-icon" :title="isLeftExpanded ? '收起工单列表' : '展开工单列表'" @click="toggleLeftSection">
                  <i :class="['el-icon-arrow-' + (isLeftExpanded ? 'left' : 'right')]"></i>
                </div>
              </div>
            </div>
            <el-table
              ref="workOrderTable"
              v-loading="tableLoading"
              :data="workOrderList"
              style="width: 100%"
              height="calc(100vh - 180px)"
              border
              element-loading-text="加载中..."
              :row-style="{ cursor: 'pointer' }"
              @row-click="handleRowClick"
            >
              <el-table-column
                v-for="(col, index) in workOrderListColumn"
                :key="index"
                :prop="col.column"
                :label="col.fieldName"
                align="center"
                :show-overflow-tooltip="true"
                :min-width="col.width"
              >
                <template slot-scope="scope">
                  <template v-if="col.column === 'createDate'">
                    {{ formatTime(scope.row.createDate) }}
                  </template>
                  <template v-else-if="col.column === 'disPlanSolutionTime' || col.column === 'disEntryOrdersDate'">
                    {{ formatTimestamp(scope.row[col.column]) }}
                  </template>
                  <template v-else-if="col.column === 'flowtype'">
                    <span :style="scope.row.flowtype === '未派工' ? 'color: red;' : ''">{{ scope.row.flowtype }}</span>
                  </template>
                  <template v-else-if="col.column === 'responseTime'">
                    <span :style="isResponseTimeOverdue(scope.row) ? 'color: red;' : ''">
                      {{ formatResponseTime(scope.row) }}
                    </span>
                  </template>
                  <template v-else>
                    {{ scope.row[col.column] }}
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </content-card>
      </div>
      <!-- 右侧内容 -->
      <div class="right-section" :class="{ hidden: isLeftExpanded }">
        <!-- 上部：工单报修和应急保洁按钮 -->
        <div class="right-top-section">
          <content-card :showTitle="false">
            <div slot="content" class="operation-buttons">
              <div class="custom-button repair-button" @click="handleCreateWorkOrder('1', '工单报修')">
                <span>工单报修</span>
              </div>
              <div class="custom-button clean-button" @click="handleCreateWorkOrder('2', '应急保洁')">
                <span>应急保洁</span>
              </div>
            </div>
          </content-card>
        </div>
        <!-- 中部：待办事项 -->
        <div class="right-middle-section">
          <div class="custom-card">
            <div class="custom-card-header">
              <div class="custom-card-title">待办事项</div>
            </div>
            <div class="custom-card-body">
              <div ref="todoListContainer" v-loading="todoLoading" class="todo-list">
                <ul v-if="backlogList.length" class="todo-items">
                  <li v-for="(item, index) in backlogList" :key="index" class="todo-item" :class="{ 'flash-item': item.isNew }" @click="openBacklogDetail(item)">
                    <div class="todo-item-header">
                      <span class="todo-source">
                        <svg-icon :name="item.workSources === '1' ? 'onestop-phone' : 'onestop-pc'" class="source-icon" />
                      </span>
                      <span class="todo-num">
                        <span v-if="item.appointmentType === '1'" class="appointment-tag">预</span>
                        <span class="work-num-text">{{ item.workNum }}</span>
                      </span>
                      <span class="todo-caller">{{ item.callerName }}</span>
                      <span class="todo-type">{{ item.flowtype }}</span>
                      <span class="todo-date"><i class="el-icon-time"></i> {{ item.createDate }}</span>
                    </div>
                    <div class="todo-item-footer">
                      <span class="todo-work-type">{{ item.workTypeName }}</span>
                      <el-button type="primary" size="mini" @click.stop="handleTodoProcess(item)">{{ item.appointmentType === '1' ? '修改' : '处理' }}</el-button>
                    </div>
                  </li>
                </ul>
                <div v-else class="empty-data">暂无待办事项</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 下部：超时工单 -->
        <div class="right-bottom-section">
          <div class="custom-card">
            <div class="custom-card-header">
              <div class="custom-card-title">完工超时</div>
            </div>
            <div class="custom-card-body">
              <div ref="timeoutListContainer" v-loading="timeoutLoading" class="timeout-list-content">
                <ul v-if="timeoutWorkOrderList.length" class="todo-items">
                  <li v-for="(item, index) in timeoutWorkOrderList" :key="index" class="todo-item" @click="openTimeoutDetail(item)">
                    <div class="todo-item-header timeout-item-header">
                      <span class="todo-source">
                        <svg-icon :name="item.workSources === '1' ? 'onestop-phone' : 'onestop-pc'" class="source-icon" />
                      </span>
                      <span class="todo-num">
                        <span class="work-num-text">{{ item.workNum }}</span>
                      </span>
                      <span class="todo-caller">{{ item.repairByName }}</span>
                      <span class="todo-type">{{ item.flowType }}</span>
                      <span class="todo-date" style="color: #f56c6c">{{ item.timeoutHours }}小时</span>
                    </div>
                    <div class="todo-item-footer">
                      <span class="todo-work-type">{{ item.workTypeName }}</span>
                      <el-button type="primary" size="mini" @click.stop="handleTimeoutProcess(item)">处理</el-button>
                    </div>
                  </li>
                </ul>
                <div v-else class="empty-data">暂无超时工单</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 创建工单弹窗 -->
    <createdWorkOrder
      v-if="workOrderDialogVisible"
      :workOrderDealShow.sync="workOrderDialogVisible"
      :dealType="getDealType()"
      :workTypeName="workTypeName"
      :workTypeCode="workTypeCode"
      :workTypeId="detailObj && detailObj.id ? detailObj.id : ''"
      :useModal="true"
      :shouldInitData="true"
      @workOrderSure="handleWorkOrderSure"
    ></createdWorkOrder>
    <el-dialog title="自定义列表参数" :visible.sync="dialogVisible" :close-on-click-modal="false" :z-index="99999" width="50%">
      <div class="setting-body" style="height: 500px">
        <el-table
          ref="el-table2"
          v-loading="customTableLoading"
          :data="customTableData"
          row-key="id"
          height="100%"
          :border="true"
          :row-class-name="getRowClassName"
          @selection-change="handleSelectionChange"
        >
          <el-table-column :selectable="checkSelectable" type="selection" width="55" :reserve-selection="true"></el-table-column>
          <el-table-column type="index" width="60" label="序号">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="参数名称" prop="fieldName"></el-table-column>
          <el-table-column label="状态" prop="unChecked" :align="'center'">
            <template slot-scope="scope">
              <span>{{ scope.row.unChecked ? '' : '必选' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="宽度" prop="width" width="220">
            <template slot-scope="scope">
              <el-input v-model="scope.row.width" placeholder="请输入宽度" @keyup.native="proving" @blur="handleWidthBlur(scope.row)">
                <template slot="append">px</template>
              </el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="confirmLoading" @click="handleConfirm">确 定</el-button>
      </template>
    </el-dialog>
    <!-- 超时工单通知 -->
    <transition name="slide-fade">
      <div v-if="showNotification" class="notification-container">
        <div class="notification-content">
          <i class="el-icon-warning-outline notification-icon"></i>
          <span>{{ notificationMessage }}</span>
        </div>
      </div>
    </transition>
    <!-- 工单详情弹窗 -->
    <el-dialog
      v-if="workOrderDetailVisible"
      :visible.sync="workOrderDetailVisible"
      custom-class="detailDialog main"
      :close-on-click-modal="false"
      :before-close="closeWorkOrderDetail"
    >
      <template slot="title">
        <span class="dialog-title">{{ dialogTitle }}</span>
      </template>
      <workOrderDetailList :rowData="detailObj" @close="closeWorkOrderDetail" />
    </el-dialog>
  </div>
</template>
<script>
import Sortable from 'sortablejs'
import workOrderDetailList from '@/views/serviceQuality/maintenance/components/workOrderDetailList.vue'
export default {
  name: 'OneStopIndex',
  components: {
    workOrderDetailList
  },
  data() {
    return {
      workOrderDialogVisible: false,
      workTypeName: '', // 工单类型名称
      workTypeCode: '', // 工单类型编码，1代表工单报修，2代表应急保洁
      workOrderList: [], // 工单列表数据
      listColumnConfig: [], // 列表列配置
      workOrderListColumn: [], // 工单列表列配置
      workOrderListCount: {}, // 工单列表数量
      currentStatus: '0', // 当前筛选状态
      isTimeOut: '0', // 是否超时工单，0否，1是
      tableLoading: false, // 表格加载状态
      pagination: {
        curPage: 1,
        pageSize: 20,
        total: 0
      }, // 分页信息
      isLastPage: false, // 是否最后一页
      loadingMore: false, // 是否正在加载更多
      customTableLoading: false, // 自定义列表参数加载状态
      customTableData: [], // 自定义列表参数数据
      dialogVisible: false, // 自定义列表参数弹窗状态
      selectData: [],
      confirmLoading: false, // 确定按钮加载状态
      backlogList: [], // 待办事项列表
      todoLoading: false, // 待办事项加载状态
      todoPagination: {
        curPage: 1,
        pageSize: 10,
        total: 0
      }, // 待办事项分页信息
      isLastTodoPage: false, // 是否最后一页
      loadingMoreTodo: false, // 是否正在加载更多待办
      backlogWebSocket: null, // 待办事项WebSocket
      refreshBacklogWebSocket: null, // 刷新待办事项WebSocket
      timeoutWorkOrderList: [], // 超时工单列表
      timeoutLoading: false, // 超时工单加载状态
      timeoutPagination: {
        curPage: 1,
        pageSize: 20,
        total: 0
      }, // 超时工单分页信息
      isLastTimeoutPage: false, // 是否最后一页
      loadingMoreTimeout: false, // 是否正在加载更多超时工单
      flashTimers: {}, // 存储闪烁效果的定时器
      noticeWebSocket: null, // 消息通知WebSocket
      showNotification: false, // 是否显示通知
      notificationMessage: '', // 通知消息内容
      notificationTimer: null, // 通知定时器
      // 新增语音队列管理
      voiceQueue: [],
      isPlayingVoice: false,
      // 工单详情相关
      workOrderDetailVisible: false, // 工单详情弹窗可见性
      detailObj: {}, // 工单详情数据
      dialogTitle: '', // 弹窗标题
      refreshTimer: null, // 工单列表自动刷新定时器
      timeoutRefreshTimer: null, // 完工超时列表自动刷新定时器
      isLeftExpanded: false // 左侧工单列表是否展开(初始状态为未展开)
    }
  },
  watch: {
    dialogVisible(newVal) {
      if (!newVal) {
        // 清空表格选中状态
        this.$nextTick(() => {
          if (this.$refs['el-table2']) {
            this.$refs['el-table2'].clearSelection()
          }
        })
      }
    },
    workOrderDialogVisible(newVal) {
      // 当工单弹窗关闭时清空detailObj
      if (!newVal) {
        this.detailObj = {}
      }
    }
  },
  created() {
    this.getListColumnConfig()
    this.getCallCenterDataNew()
    this.getCallCenterDataNewCount()
    this.getBacklogList()
    this.getWorkOrderTimeoutList()
    this.initBacklogWebSocket() // 初始化待办事项WebSocket
    this.initNoticeWebSocket() // 初始化消息通知WebSocket
    this.initRefreshBacklogWebSocket() // 初始化刷新待办事项WebSocket
    // 初始状态为全部时，启动自动刷新
    if (this.currentStatus === '0') {
      this.startAutoRefresh()
    }
    // 启动完工超时列表自动刷新
    this.startTimeoutAutoRefresh()
  },
  mounted() {
    // 监听表格滚动事件
    this.$nextTick(() => {
      const tableBodyWrapper = this.$refs.workOrderTable.$el.querySelector('.el-table__body-wrapper')
      if (tableBodyWrapper) {
        tableBodyWrapper.addEventListener('scroll', this.handleTableBodyScroll)
      }
      // 监听待办事项滚动事件
      const todoListContainer = this.$refs.todoListContainer
      if (todoListContainer) {
        todoListContainer.addEventListener('scroll', this.handleTodoListScroll)
      }
      // 监听超时工单滚动事件
      const timeoutListContainer = this.$refs.timeoutListContainer
      if (timeoutListContainer) {
        timeoutListContainer.addEventListener('scroll', this.handleTimeoutListScroll)
      }
    })
  },
  beforeDestroy() {
    // 移除事件监听，防止内存泄漏
    const tableBodyWrapper = this.$refs.workOrderTable.$el.querySelector('.el-table__body-wrapper')
    if (tableBodyWrapper) {
      tableBodyWrapper.removeEventListener('scroll', this.handleTableBodyScroll)
    }
    // 移除待办事项滚动监听
    const todoListContainer = this.$refs.todoListContainer
    if (todoListContainer) {
      todoListContainer.removeEventListener('scroll', this.handleTodoListScroll)
    }
    // 移除超时工单滚动监听
    const timeoutListContainer = this.$refs.timeoutListContainer
    if (timeoutListContainer) {
      timeoutListContainer.removeEventListener('scroll', this.handleTimeoutListScroll)
    }
    // 关闭WebSocket连接
    this.closeBacklogWebSocket()
    // 关闭刷新待办事项WebSocket连接
    this.closeRefreshBacklogWebSocket()
    // 关闭消息通知WebSocket连接
    this.closeNoticeWebSocket()
    // 清除自动刷新定时器
    this.stopAutoRefresh()
    // 清除完工超时列表自动刷新定时器
    this.stopTimeoutAutoRefresh()
  },
  methods: {
    // 开始自动刷新
    startAutoRefresh() {
      // 先清除可能存在的定时器
      this.stopAutoRefresh()
      // 设置30秒刷新一次
      this.refreshTimer = setInterval(() => {
        // 只有在全部状态下才刷新
        if (this.currentStatus === '0') {
          console.log('自动刷新工单列表数据')
          this.getCallCenterDataNew()
          this.getCallCenterDataNewCount()
        } else {
          // 如果状态改变了，停止自动刷新
          this.stopAutoRefresh()
        }
      }, 30000) // 30秒
    },
    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    // 开始完工超时列表自动刷新
    startTimeoutAutoRefresh() {
      // 先清除可能存在的定时器
      this.stopTimeoutAutoRefresh()
      // 设置2分钟刷新一次
      this.timeoutRefreshTimer = setInterval(() => {
        console.log('自动刷新完工超时列表数据')
        this.getWorkOrderTimeoutList()
      }, 120000) // 2分钟
    },
    // 停止完工超时列表自动刷新
    stopTimeoutAutoRefresh() {
      if (this.timeoutRefreshTimer) {
        clearInterval(this.timeoutRefreshTimer)
        this.timeoutRefreshTimer = null
      }
    },
    proving(e) {
      // 仅保留数字过滤逻辑
      if (e.target.value) {
        e.target.value = e.target.value.toString().replace(/[^0-9]/g, '')
      }
    },
    // 为表格行添加CSS类名，用于拖拽样式
    getRowClassName() {
      return 'draggable-row'
    },
    handleWidthBlur(row) {
      // 失焦时进行最终校验
      if (!row.width || parseInt(row.width) < 50) {
        row.width = '50'
      } else if (parseInt(row.width) > 1000) {
        // 可选的最大值限制
        row.width = '1000'
      }
    },
    handleConfirm() {
      if (this.confirmLoading) return // 防止重复点击
      if (!this.customTableData.length) {
        this.$message.error('暂无数据可保存！')
        return false
      }
      this.confirmLoading = true // 设置按钮为加载状态
      this.customTableData.forEach((item) => {
        const isSelected = this.selectData.find((item1) => item1.column === item.column && item1.fieldName === item.fieldName)
        if (isSelected) {
          item.isChecked = '1'
        } else {
          item.isChecked = '0'
        }
        if (item.isInit) {
          delete item.id
          delete item.isInit
        }
      })
      // 容错处理：优先取 user.userId，如果取不到则取 userId
      const userId = this.$store.state.user.userInfo.user?.userId || this.$store.state.user.userInfo.userId
      let params = {
        moduleId: 'M0',
        configParams: JSON.stringify(this.customTableData),
        userId: userId
      }
      this.$api
        .saveListColumnConfig(params)
        .then((res) => {
          this.$message.success('保存成功！')
          this.dialogVisible = false
          this.getListColumnConfig()
        })
        .catch((err) => {
          console.error('保存失败:', err)
          this.$message.error('保存失败，请重试')
        })
        .finally(() => {
          this.customTableLoading = false
          this.confirmLoading = false // 恢复按钮状态
        })
    },
    checkSelectable(val) {
      return val.unChecked
    },
    handleSelectionChange(val) {
      this.selectData = val
    },
    getCallCenterDataNewCount() {
      this.$api
        .callCenterDataNewCount({
          selectType: this.currentStatus,
          isTimeOut: this.isTimeOut,
          num: Math.random()
        })
        .then((res) => {
          if (res.success) {
            this.workOrderListCount = res.body
            // 更新总数量
            this.pagination.total = parseInt(this.workOrderListCount.countAllOrder || 0)
          }
        })
    },
    getCallCenterDataNew(isLoadMore = false) {
      if (this.isLastPage && isLoadMore) return
      if (!isLoadMore) {
        // 不是加载更多，重置分页
        this.pagination.curPage = 1
        this.isLastPage = false
      }
      if (isLoadMore) {
        this.loadingMore = true
      } else {
        this.tableLoading = true
      }
      this.$api
        .callCenterDataNew({
          curPage: this.pagination.curPage,
          pageSize: this.pagination.pageSize,
          selectType: this.currentStatus,
          isTimeOut: this.isTimeOut,
          num: Math.random()
        })
        .then((res) => {
          console.log('callCenterDataNew', res)
          if (res.success) {
            if (isLoadMore) {
              // 加载更多，追加数据
              this.workOrderList = [...this.workOrderList, ...res.body.result]
            } else {
              // 首次加载，替换数据
              this.workOrderList = res.body.result
            }
            // 判断是否最后一页
            if (!res.body.result || res.body.result.length < this.pagination.pageSize) {
              this.isLastPage = true
            }
          }
        })
        .finally(() => {
          this.tableLoading = false
          this.loadingMore = false
        })
    },
    formatTime(dateStr) {
      if (!dateStr) return ''
      // 只保留时间部分 (时:分:秒)
      return dateStr.split(' ')[1]
    },
    // 格式化时间戳为字符串
    formatTimestamp(timestamp) {
      if (!timestamp) return ''
      // 如果已经是字符串格式，直接返回
      if (typeof timestamp === 'string') return timestamp
      // 将时间戳转换为日期字符串
      const date = new Date(timestamp)
      if (isNaN(date.getTime())) return ''

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = date.getHours()
      const minutes = date.getMinutes()
      const seconds = date.getSeconds()

      // 如果时分秒都是0，只显示日期
      if (hours === 0 && minutes === 0 && seconds === 0) {
        return `${year}-${month}-${day}`
      }

      // 否则显示完整的日期时间
      const hoursStr = String(hours).padStart(2, '0')
      const minutesStr = String(minutes).padStart(2, '0')
      const secondsStr = String(seconds).padStart(2, '0')
      return `${year}-${month}-${day} ${hoursStr}:${minutesStr}:${secondsStr}`
    },
    // 格式化响应时间显示
    formatResponseTime(row) {
      // 未派工状态
      if (row.flowcode == '2' && !row.responseTime) {
        if (this.isResponseTimeOverdue(row)) {
          return '已超时'
        } else {
          return '待派工'
        }
      } else {
        if (row.responseTime == undefined || row.responseTime == null || row.responseTime === '') {
          return ''
        } else {
          return row.responseTime + '分钟'
        }
      }
    },
    compareTime(date) {
      if (!date) return 0
      const now = new Date()
      const targetDate = new Date(date)
      return Math.floor((now - targetDate) / (1000 * 60))
    },
    // 判断响应时间是否超时
    isResponseTimeOverdue(row) {
      // 未派工状态
      if (row.flowcode == '2' && !row.responseTime) {
        // 订餐服务
        if (row.workTypeCode == '4') {
          return this.compareTime(row.havemealsDate) >= row.overtime
        } else if (row.appointmentDate != undefined && row.appointmentDate != '') {
          return this.compareTime(row.appointmentDate) >= row.overtime
        } else {
          return this.compareTime(row.designateDeptDate) >= row.overtime
        }
      } else {
        return row.responseTime >= 15
      }
    },
    getListColumnConfig() {
      // 容错处理：优先取 user.userId，如果取不到则取 userId
      const userId = this.$store.state.user.userInfo.user?.userId || this.$store.state.user.userInfo.userId
      this.$api.getListColumnConfig({ moduleId: 'M0', userId: userId }).then((res) => {
        console.log(res)
        if (res.code == '200') {
          this.listColumnConfig = JSON.parse(res.data.configParams)
          this.workOrderListColumn = this.listColumnConfig.filter((item) => item.isChecked === '1')
        }
      })
    },
    handleCreateWorkOrder(workTypeCode, workTypeName) {
      this.workTypeCode = workTypeCode
      this.workTypeName = workTypeName
      this.workOrderDialogVisible = true
    },
    handleWorkOrderSure(data) {
      console.log('工单创建/处理成功', data)
      this.workOrderDialogVisible = false
      // 清空工单详情数据
      this.detailObj = {}
      // 刷新工单列表数据
      this.getCallCenterDataNew()
      this.getCallCenterDataNewCount()
      // 刷新待办列表
      this.getBacklogList()
      // 刷新超时工单列表
      this.getWorkOrderTimeoutList()
    },
    handleStatusChange(status) {
      this.currentStatus = status
      // 切换状态时清除超时筛选
      this.isTimeOut = '0'
      // 重置分页
      this.pagination.curPage = 1
      this.isLastPage = false
      this.getCallCenterDataNew()
      this.getCallCenterDataNewCount()

      // 根据当前状态决定是否启动自动刷新
      if (status === '0') {
        this.startAutoRefresh()
      } else {
        this.stopAutoRefresh()
      }
    },
    handleTimeoutClick() {
      // 点击超时工单按钮，设置selectType为5，isTimeOut为1
      this.currentStatus = this.currentStatus === '5' ? '0' : '5'
      this.isTimeOut = '1'
      // 重置分页
      this.pagination.curPage = 1
      this.isLastPage = false
      this.getCallCenterDataNew()
      this.getCallCenterDataNewCount()
    },
    handleTableBodyScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target
      // 滚动到底部50px时加载更多
      if (scrollTop + clientHeight >= scrollHeight - 50 && !this.tableLoading && !this.loadingMore && !this.isLastPage) {
        this.pagination.curPage++
        this.getCallCenterDataNew(true)
      }
    },
    initSort() {
      // 获取正确的表格元素
      const settingTable = document.querySelector('.setting-body .el-table__body-wrapper > table > tbody')
      if (!settingTable) return
      const ops = {
        animation: 200,
        handle: '.el-table__row',
        ghostClass: 'sortable-ghost', // 拖动时的样式类
        chosenClass: 'sortable-chosen', // 选中时的样式类
        dragClass: 'sortable-drag', // 拖拽过程中的样式类
        forceFallback: false, // 使用HTML5原生拖拽
        fallbackClass: 'sortable-fallback', // 降级时的样式类
        onStart: (evt) => {
          console.log('开始拖动:', evt.oldIndex)
          // 为拖拽元素添加拖拽中的样式
          evt.item.style.cursor = 'grabbing'
        },
        onEnd: (evt) => {
          // 恢复光标样式
          evt.item.style.cursor = 'grab'
          // 创建数组副本
          const temp = [...this.customTableData]
          // 获取被拖动的行
          const draggedRow = temp.splice(evt.oldIndex, 1)[0]
          // 将行插入新位置
          temp.splice(evt.newIndex, 0, draggedRow)
          // 更新数据
          this.customTableData = temp
          // 强制更新视图
          this.$nextTick(() => {
            console.log('拖动后:', this.customTableData)
          })
        }
      }
      // 创建可排序实例
      Sortable.create(settingTable, ops)
    },
    getCustomTableData() {
      this.customTableLoading = true
      // 容错处理：优先取 user.userId，如果取不到则取 userId
      const userId = this.$store.state.user.userInfo.user?.userId || this.$store.state.user.userInfo.userId
      this.$api
        .getListColumnConfig({
          userId: userId,
          moduleId: 'M0'
        })
        .then((res) => {
          this.customTableLoading = false
          this.customTableData = JSON.parse(res.data.configParams)
          this.customTableData.forEach((item) => {
            if (!item.id) {
              item.id = Math.floor(10000 + Math.random() * 90000)
              item.isInit = true
            }
            if (!item.unChecked || item.isChecked === '1') {
              this.$refs['el-table2'].toggleRowSelection(item, true)
            }
          })
        })
        .finally(() => {
          this.customTableLoading = false
        })
    },
    handleSettingClick() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.initSort()
        this.getCustomTableData()
      })
    },
    getBacklogList(isLoadMore = false) {
      if (this.isLastTodoPage && isLoadMore) return
      if (!isLoadMore) {
        // 不是加载更多，重置分页
        this.todoPagination.curPage = 1
        this.isLastTodoPage = false
      }
      if (isLoadMore) {
        this.loadingMoreTodo = true
      } else {
        this.todoLoading = true
      }
      this.$api
        .getBacklogList({
          num: Math.random(),
          isComplaints: '0',
          toExt: '',
          curPage: this.todoPagination.curPage,
          pageSize: this.todoPagination.pageSize
        })
        .then((res) => {
          console.log('getBacklogList', res)
          if (res.success) {
            if (isLoadMore) {
              // 加载更多，追加数据
              this.backlogList = [...this.backlogList, ...res.body.list]
            } else {
              // 首次加载，替换数据
              this.backlogList = res.body.list
            }
            // 更新总数
            this.todoPagination.total = res.body.totalRecord || 0
            // 判断是否最后一页
            if (!res.body.list || res.body.list.length < this.todoPagination.pageSize) {
              this.isLastTodoPage = true
            }
          }
        })
        .finally(() => {
          this.todoLoading = false
          this.loadingMoreTodo = false
        })
    },
    // 处理待办事项滚动
    handleTodoListScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target
      // 滚动到底部30px时加载更多
      if (scrollTop + clientHeight >= scrollHeight - 30 && !this.todoLoading && !this.loadingMoreTodo && !this.isLastTodoPage) {
        this.todoPagination.curPage++
        this.getBacklogList(true)
      }
    },
    // 处理待办事项
    handleTodoProcess(item) {
      console.log('处理待办事项', item)
      // 设置工单类型信息
      this.workTypeCode = item.workTypeCode
      this.workTypeName = item.workTypeName
      this.detailObj = item
      // 打开工单处理弹窗
      this.workOrderDialogVisible = true
    },
    getWorkOrderTimeoutList(isLoadMore = false) {
      if (this.isLastTimeoutPage && isLoadMore) return
      if (!isLoadMore) {
        // 不是加载更多，重置分页
        this.timeoutPagination.curPage = 1
        this.isLastTimeoutPage = false
      }
      if (isLoadMore) {
        this.loadingMoreTimeout = true
      } else {
        this.timeoutLoading = true
      }
      this.$api
        .getWorkOrderTimeoutList({
          num: Math.random(),
          currentPage: this.timeoutPagination.curPage,
          pageSize: this.timeoutPagination.pageSize
        })
        .then((res) => {
          console.log('getWorkOrderTimeoutList', res)
          if (res.code == '200') {
            if (isLoadMore) {
              // 加载更多，追加数据
              this.timeoutWorkOrderList = [...this.timeoutWorkOrderList, ...res.data.list]
            } else {
              // 首次加载，替换数据
              this.timeoutWorkOrderList = res.data.list
            }
            // 更新总数
            this.timeoutPagination.total = res.data.total || 0
            // 判断是否最后一页
            if (!res.data.list || res.data.list.length < this.timeoutPagination.pageSize) {
              this.isLastTimeoutPage = true
            }
          }
        })
        .finally(() => {
          this.timeoutLoading = false
          this.loadingMoreTimeout = false
        })
    },
    // 处理超时工单滚动
    handleTimeoutListScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target
      // 滚动到底部30px时加载更多
      if (scrollTop + clientHeight >= scrollHeight - 30 && !this.timeoutLoading && !this.loadingMoreTimeout && !this.isLastTimeoutPage) {
        this.timeoutPagination.curPage++
        this.getWorkOrderTimeoutList(true)
      }
    },
    // 处理超时工单
    handleTimeoutProcess(item) {
      console.log('处理超时工单', item)
      // 设置工单详情数据
      this.detailObj = item
      // 设置弹窗标题
      if (item.workTypeName) {
        this.dialogTitle = `${item.workTypeName}（${item.flowType || ''}）`
      } else {
        this.dialogTitle = '工单详情'
      }
      // 打开工单详情弹窗，而不是工单处理弹窗
      this.workOrderDetailVisible = true
    },
    // 初始化待办事项WebSocket
    initBacklogWebSocket() {
      // 关闭已有的连接
      this.closeBacklogWebSocket()
      // 获取当前用户ID
      const userInfo = this.$store.state.user.userInfo.user
      if (!userInfo || !userInfo.staffId) {
        console.error('用户未登录或无法获取用户ID')
        return
      }
      try {
        // 创建WebSocket连接
        this.backlogWebSocket = this.$api.homeOperWorkOrderWebSocketServer('')
        // 连接建立时的处理
        this.backlogWebSocket.onopen = () => {
          this.sendMessage()
          console.log('待办事项WebSocket连接已建立')
        }
        // 接收消息的处理
        this.backlogWebSocket.onmessage = (event) => {
          try {
            console.log('收到待办事项WebSocket消息:', event.data)
            // 解析WebSocket返回的数据
            const newItem = JSON.parse(event.data)
            // 将新数据插入到列表顶部
            if (newItem && newItem.workNum) {
              // 先移除之前所有项目的闪烁效果
              this.backlogList.forEach((item) => {
                item.isNew = false
              })
              // 添加闪烁标记
              newItem.isNew = true
              this.backlogList.unshift(newItem)
              // 设置较大阈值，避免列表过长影响性能
              if (this.backlogList.length > 500) {
                this.backlogList = this.backlogList.slice(0, 500)
              }
              // 使用工单号作为定时器的标识，避免多个定时器冲突
              if (this.flashTimers && this.flashTimers[newItem.workNum]) {
                clearTimeout(this.flashTimers[newItem.workNum])
              }
              // 初始化flashTimers对象（如果不存在）
              if (!this.flashTimers) {
                this.flashTimers = {}
              }
              // 3秒后移除闪烁效果
              this.flashTimers[newItem.workNum] = setTimeout(() => {
                if (this.backlogList.length > 0) {
                  const index = this.backlogList.findIndex((item) => item.workNum === newItem.workNum)
                  if (index !== -1) {
                    this.backlogList[index].isNew = false
                  }
                }
                delete this.flashTimers[newItem.workNum]
              }, 3000)
            }
          } catch (error) {
            console.error('处理WebSocket消息出错:', error)
          }
        }
        // 连接关闭的处理
        this.backlogWebSocket.onclose = (event) => {
          console.log('待办事项WebSocket连接已关闭:', event)
          // 尝试重新连接
          if (!this.isDestroyed) {
            setTimeout(() => {
              this.initBacklogWebSocket()
            }, 3000) // 3秒后尝试重连
          }
        }
        // 连接错误的处理
        this.backlogWebSocket.onerror = (error) => {
          console.error('待办事项WebSocket连接错误:', error)
        }
      } catch (error) {
        console.error('初始化待办事项WebSocket出错:', error)
      }
    },
    // 关闭待办事项WebSocket连接
    closeBacklogWebSocket() {
      if (this.backlogWebSocket) {
        try {
          this.backlogWebSocket.close()
        } catch (error) {
          console.error('关闭待办事项WebSocket出错:', error)
        }
        this.backlogWebSocket = null
      }
    },
    sendMessage() {
      this.backlogWebSocket.send(this.$store.state.user.userInfo.user.hospitalCode)
    },
    // 初始化消息通知WebSocket
    initNoticeWebSocket() {
      // 关闭已有的连接
      this.closeNoticeWebSocket()
      // 获取当前用户ID
      const userInfo = this.$store.state.user.userInfo.user
      if (!userInfo || !userInfo.staffId) {
        console.error('用户未登录或无法获取用户ID')
        return
      }
      try {
        // 创建WebSocket连接
        this.noticeWebSocket = this.$api.noticeMessageWebSocketServer('')
        // 连接建立时的处理
        this.noticeWebSocket.onopen = () => {
          console.log('消息通知WebSocket连接已建立')
          // 获取用户信息
          const userInfo = this.$store.state.user.userInfo.user
          // 构建初始化消息，使用逗号拼接的字符串
          const initMessage = [userInfo.hospitalCode || '', userInfo.deptId || '', userInfo.type || '1', userInfo.staffId || ''].join(',')
          // 发送初始化消息
          console.log('发送初始化消息:', initMessage)
          this.noticeWebSocket.send(initMessage)
        }
        // 接收消息的处理
        this.noticeWebSocket.onmessage = (event) => {
          try {
            console.log('收到消息通知WebSocket消息:', event.data)
            // 解析WebSocket返回的数据
            const noticeData = JSON.parse(event.data)
            // 检查identifying的值
            if (noticeData && noticeData.identifying === '4') {
              // 获取当前用户部门ID
              const currentDeptId = this.$store.state.user.userInfo.user.deptId || ''
              // 检查部门权限
              if (noticeData.depts && (noticeData.depts.indexOf(currentDeptId) !== -1 || noticeData.depts.indexOf('all') !== -1)) {
                // 部门匹配，显示通知
                console.log('部门匹配，显示超时工单通知')
                this.showTimeoutNotification()
              } else {
                console.log('部门不匹配，不显示通知', {
                  currentDeptId,
                  messageDepts: noticeData.depts
                })
              }
            } else if (noticeData && noticeData.identifying === '0') {
              // 新工单提醒
              console.log('收到新工单提醒')
              this.showNewWorkOrderNotification()
            }
          } catch (error) {
            console.error('处理消息通知WebSocket消息出错:', error)
          }
        }
        // 连接关闭的处理
        this.noticeWebSocket.onclose = (event) => {
          console.log('消息通知WebSocket连接已关闭:', event)
          // 尝试重新连接
          if (!this.isDestroyed) {
            setTimeout(() => {
              this.initNoticeWebSocket()
            }, 3000) // 3秒后尝试重连
          }
        }
        // 连接错误的处理
        this.noticeWebSocket.onerror = (error) => {
          console.error('消息通知WebSocket连接错误:', error)
        }
      } catch (error) {
        console.error('初始化消息通知WebSocket出错:', error)
      }
    },
    // 关闭消息通知WebSocket连接
    closeNoticeWebSocket() {
      if (this.noticeWebSocket) {
        try {
          this.noticeWebSocket.close()
        } catch (error) {
          console.error('关闭消息通知WebSocket出错:', error)
        }
        this.noticeWebSocket = null
      }
    },
    // 初始化刷新待办事项WebSocket
    initRefreshBacklogWebSocket() {
      // 关闭已有的连接
      this.closeRefreshBacklogWebSocket()
      // 获取当前用户ID
      const userInfo = this.$store.state.user.userInfo.user
      if (!userInfo || !userInfo.staffId) {
        console.error('用户未登录或无法获取用户ID')
        return
      }
      try {
        // 创建WebSocket连接
        this.refreshBacklogWebSocket = this.$api.homeRefreshBacklogWebSocketServer('')
        // 连接建立时的处理
        this.refreshBacklogWebSocket.onopen = () => {
          // 发送医院代码
          const hospitalCode = this.$store.state.user.userInfo.user.hospitalCode || ''
          this.refreshBacklogWebSocket.send(hospitalCode)
          console.log('刷新待办事项WebSocket连接已建立，发送医院代码：', hospitalCode)
        }
        // 接收消息的处理
        this.refreshBacklogWebSocket.onmessage = (event) => {
          try {
            console.log('收到刷新待办事项WebSocket消息:', event.data)
            // 收到消息后刷新待办事项列表
            this.getBacklogList()
          } catch (error) {
            console.error('处理刷新待办事项WebSocket消息出错:', error)
          }
        }
        // 连接关闭的处理
        this.refreshBacklogWebSocket.onclose = (event) => {
          console.log('刷新待办事项WebSocket连接已关闭:', event)
          // 尝试重新连接
          if (!this.isDestroyed) {
            setTimeout(() => {
              this.initRefreshBacklogWebSocket()
            }, 3000) // 3秒后尝试重连
          }
        }
        // 连接错误的处理
        this.refreshBacklogWebSocket.onerror = (error) => {
          console.error('刷新待办事项WebSocket连接错误:', error)
        }
      } catch (error) {
        console.error('初始化刷新待办事项WebSocket出错:', error)
      }
    },
    // 关闭刷新待办事项WebSocket连接
    closeRefreshBacklogWebSocket() {
      if (this.refreshBacklogWebSocket) {
        try {
          this.refreshBacklogWebSocket.close()
        } catch (error) {
          console.error('关闭刷新待办事项WebSocket出错:', error)
        }
        this.refreshBacklogWebSocket = null
      }
    },
    // 测试刷新待办事项
    testRefreshBacklog() {
      console.log('测试刷新待办事项')
      // 手动刷新待办事项列表
      this.getBacklogList()
    },
    // 播放语音提示
    playVoiceNotification(text) {
      // 将语音文本添加到队列
      this.voiceQueue.push(text)
      // 如果当前没有正在播放的语音，则开始播放
      if (!this.isPlayingVoice) {
        this.processVoiceQueue()
      }
    },

    // 处理语音队列
    processVoiceQueue() {
      // 如果队列为空，标记为不在播放状态并退出
      if (this.voiceQueue.length === 0) {
        this.isPlayingVoice = false
        return
      }

      // 标记为正在播放状态
      this.isPlayingVoice = true

      // 从队列中取出第一条语音文本
      const text = this.voiceQueue.shift()

      // 使用Web Speech API播放语音
      if ('speechSynthesis' in window) {
        // 取消所有正在进行的语音
        window.speechSynthesis.cancel()

        // 创建语音合成对象
        const speech = new SpeechSynthesisUtterance(text)
        // 设置语音为中文
        speech.lang = 'zh-CN'
        // 设置语音速度（1.0是正常速度）
        speech.rate = 1.0
        // 设置音量
        speech.volume = 1.0

        // 添加播放完成事件监听器
        speech.onend = () => {
          console.log('语音播放完成')
          // 继续处理队列中的下一条语音
          this.$nextTick(() => {
            this.processVoiceQueue()
          })
        }

        // 添加错误处理
        speech.onerror = (event) => {
          console.error('语音播放错误:', event)
          // 继续处理队列中的下一条语音
          this.$nextTick(() => {
            this.processVoiceQueue()
          })
        }

        // 播放语音
        window.speechSynthesis.speak(speech)
      } else {
        console.warn('浏览器不支持语音合成API')
        // 浏览器不支持语音合成，直接处理下一条
        this.$nextTick(() => {
          this.processVoiceQueue()
        })
      }
    },

    // 显示超时工单通知
    showTimeoutNotification() {
      // 设置通知消息
      this.notificationMessage = '您有新超时工单，请及时查看！'
      // 显示通知
      this.showNotification = true
      // 播放语音提示
      this.playVoiceNotification(this.notificationMessage)
      // 清除之前的定时器
      if (this.notificationTimer) {
        clearTimeout(this.notificationTimer)
      }
      // 5秒后自动关闭通知
      this.notificationTimer = setTimeout(() => {
        this.showNotification = false
      }, 5000)
    },
    // 显示新工单通知
    showNewWorkOrderNotification() {
      // 设置通知消息
      this.notificationMessage = '您有新工单，请及时处理！'
      // 显示通知
      this.showNotification = true
      // 播放语音提示
      this.playVoiceNotification(this.notificationMessage)
      // 清除之前的定时器
      if (this.notificationTimer) {
        clearTimeout(this.notificationTimer)
      }
      // 5秒后自动关闭通知
      this.notificationTimer = setTimeout(() => {
        this.showNotification = false
      }, 5000)
    },
    // 测试超时工单通知
    testTimeoutNotification() {
      // 模拟收到identifying为4的消息
      const currentDeptId = this.$store.state.user.userInfo.user.deptId || ''
      // 模拟消息数据
      const mockNoticeData = {
        identifying: '4',
        depts: ['all', currentDeptId] // 包含'all'和当前部门ID，确保测试时能显示通知
      }
      console.log('测试超时工单通知', mockNoticeData)
      // 检查部门权限
      if (mockNoticeData.depts && (mockNoticeData.depts.indexOf(currentDeptId) !== -1 || mockNoticeData.depts.indexOf('all') !== -1)) {
        // 部门匹配，显示通知
        console.log('部门匹配，显示超时工单通知')
        this.showTimeoutNotification()
      } else {
        console.log('部门不匹配，不显示通知', {
          currentDeptId,
          messageDepts: mockNoticeData.depts
        })
      }
    },
    // 测试新工单通知
    testNewWorkOrderNotification() {
      console.log('测试新工单通知')
      this.showNewWorkOrderNotification()
    },
    // 模拟新的待办事项
    simulateNewBacklog() {
      // 模拟WebSocket返回的数据
      const mockData = {
        callerName: '模拟用户',
        createDate: new Date().toISOString().replace('T', ' ').substring(0, 19),
        flowcode: '1',
        flowtype: '未受理',
        hospitalCode: 'SINOMIS',
        id: 'mock_' + Date.now(),
        projectName: 'ioms',
        projectType: 'staffServices',
        pushType: '0',
        sourcesPhone: '18192116051',
        type: '2',
        urgencyDegree: '2',
        workNum:
          'WX' +
          new Date().toISOString().substring(2, 10).replace(/-/g, '') +
          '-' +
          Math.floor(Math.random() * 1000)
            .toString()
            .padStart(3, '0'),
        workSources: Math.random() > 0.5 ? '1' : '2',
        workTypeCode: '1',
        workTypeName: '工单报修',
        appointmentType: Math.random() > 0.7 ? '1' : '0'
      }
      // 先移除之前所有项目的闪烁效果
      this.backlogList.forEach((item) => {
        item.isNew = false
      })
      // 添加闪烁标记
      mockData.isNew = true
      // 将模拟数据添加到列表顶部
      this.backlogList.unshift(mockData)
      // 设置较大阈值，避免列表过长影响性能
      if (this.backlogList.length > 500) {
        this.backlogList = this.backlogList.slice(0, 500)
      }
      // 使用工单号作为定时器的标识，避免多个定时器冲突
      if (this.flashTimers && this.flashTimers[mockData.workNum]) {
        clearTimeout(this.flashTimers[mockData.workNum])
      }
      // 初始化flashTimers对象（如果不存在）
      if (!this.flashTimers) {
        this.flashTimers = {}
      }
      // 3秒后移除闪烁效果
      this.flashTimers[mockData.workNum] = setTimeout(() => {
        if (this.backlogList.length > 0) {
          const index = this.backlogList.findIndex((item) => item.workNum === mockData.workNum)
          if (index !== -1) {
            this.backlogList[index].isNew = false
          }
        }
        delete this.flashTimers[mockData.workNum]
      }, 3000)
    },
    handleRowClick(row) {
      // 点击工单列表行时，如果是暂存状态(flowcode是7)，按照待办事项的处理方法走
      if (row.flowcode === '7') {
        // 暂存状态，按照待办事项处理方法
        console.log('处理暂存状态工单', row)
        // 设置工单类型信息
        this.workTypeCode = row.workTypeCode
        this.workTypeName = row.workTypeName
        this.detailObj = row
        // 打开工单处理弹窗
        this.workOrderDialogVisible = true
      } else {
        // 其他状态，打开工单详情弹窗
        this.detailObj = row
        if (this.detailObj.flowtype) {
          this.dialogTitle = `${this.detailObj.workTypeName}（${this.detailObj.flowtype}）`
        } else {
          this.dialogTitle = `${this.detailObj.workTypeName}`
        }
        this.workOrderDetailVisible = true
      }
    },
    // 获取处理类型
    getDealType() {
      // 如果是暂存状态的工单，使用特殊的处理模式
      if (this.detailObj && this.detailObj.flowcode === '7') {
        return 'dealToAdd' // 暂存工单：回显用deal，提交用add
      }
      // 原有逻辑
      if (this.detailObj && this.detailObj.appointmentType === '1') {
        return 'update'
      }
      if (this.detailObj && this.detailObj.id) {
        return 'deal'
      }
      return 'add'
    },
    closeWorkOrderDetail() {
      this.workOrderDetailVisible = false
      // 清空工单详情数据
      this.detailObj = {}
      // 刷新工单列表
      this.getCallCenterDataNew()
      this.getCallCenterDataNewCount()
      // 刷新待办列表
      this.getBacklogList()
      // 刷新超时工单列表
      this.getWorkOrderTimeoutList()
    },
    // 打开待办事项详情
    openBacklogDetail(item) {
      console.log('打开待办事项详情', item)
      // 设置工单详情数据
      this.detailObj = item
      // 标记这是查看模式，需要隐藏处理节点
      this.detailObj.isViewMode = true
      // 设置弹窗标题
      if (item.workTypeName) {
        this.dialogTitle = `${item.workTypeName}（${item.flowtype || ''}）`
      } else {
        this.dialogTitle = '工单详情'
      }
      // 打开工单详情弹窗
      this.workOrderDetailVisible = true
    },
    // 打开超时工单详情
    openTimeoutDetail(item) {
      console.log('打开超时工单详情', item)
      // 设置工单详情数据
      this.detailObj = item
      // 标记这是查看模式，需要隐藏处理节点
      this.detailObj.isViewMode = true
      // 设置弹窗标题
      if (item.workTypeName) {
        this.dialogTitle = `${item.workTypeName}（${item.flowType || ''}）`
      } else {
        this.dialogTitle = '工单详情'
      }
      // 打开工单详情弹窗
      this.workOrderDetailVisible = true
    },
    toggleLeftSection() {
      this.isLeftExpanded = !this.isLeftExpanded
      // 延迟执行，等待DOM更新完成后重新计算表格布局
      this.$nextTick(() => {
        // 重新计算表格布局
        if (this.$refs.workOrderTable) {
          this.$refs.workOrderTable.doLayout()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.one-stop-service {
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}
.test-btn {
  position: absolute;
  top: 10px;
  right: 20px;
  z-index: 100;
}
.test-timeout-btn {
  position: absolute;
  top: 10px;
  right: 130px;
  z-index: 100;
}
.test-new-btn {
  position: absolute;
  top: 10px;
  right: 260px;
  z-index: 100;
}
.service-layout {
  display: flex;
  height: calc(100vh - 100px);
  gap: 20px;
  transition: all 0.3s ease;
}
.service-layout.expanded {
  /* 默认就是row，无需重复声明 */
}
.left-section {
  flex: 6;
  height: 100%;
  min-width: 600px;
  transition: all 0.3s ease;
}
.left-section.expanded {
  flex: 1;
  min-width: 100%;
}
.right-section {
  flex: 4;
  min-width: 300px;
  max-width: 500px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  transition: all 0.3s ease;
  opacity: 1;
}
.right-section.hidden {
  width: 0;
  min-width: 0;
  max-width: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
  opacity: 0;
}
.right-top-section {
  height: 80px;
}
.right-middle-section {
  height: calc(58% - 80px);
}
.right-bottom-section {
  height: calc(44% - 38px);
}
.operation-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
}
.custom-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 50px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}
.repair-button {
  background: #3562db;
}
.repair-button:hover {
  background: #2c50b8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.clean-button {
  background: #52c41a;
}
.clean-button:hover {
  background: #389e0d;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.custom-card {
  background: #fff;
  border-radius: 4px;
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.custom-card-header {
  height: 25px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.custom-card-title {
  font-size: 15px;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}
.custom-card-title::before {
  content: '';
  width: 4px;
  height: 15px;
  background: #3562db;
  border-radius: 2px;
  margin-right: 6px;
}
.custom-card-body {
  flex: 1;
  position: relative;
  overflow: hidden;
}
.timeout-list-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}
.todo-list {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}
.todo-items {
  padding: 0;
  margin: 0;
  list-style: none;
}
.todo-item {
  padding: 10px 8px;
  margin-bottom: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}
/* 只对待办事项区域的列表项添加指针样式 */
.right-middle-section .todo-item,
.right-bottom-section .todo-item {
  cursor: pointer;
}
.todo-item:hover {
  background-color: #f2f6fc;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}
.todo-item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
  align-items: center;
}
.timeout-item-header {
  .todo-num {
    width: 140px;
    min-width: 140px;
  }
  .todo-caller,
  .todo-type {
    margin: 0 3px;
  }
}
.todo-source {
  margin-right: 6px;
  width: 20px;
  min-width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.source-icon {
  font-size: 16px;
  color: #3562db;
}
.todo-num {
  font-weight: bold;
  color: #303133;
  display: flex;
  align-items: center;
  width: 140px;
  min-width: 140px;
}
.work-num-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}
.appointment-tag {
  display: inline-block;
  width: 20px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  background-color: #f56c6c;
  color: white;
  font-size: 12px;
  border-radius: 2px;
  margin-right: 6px;
  font-weight: normal;
  flex-shrink: 0;
}
.todo-caller {
  width: 50px;
  min-width: 50px;
  margin: 0 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.todo-type {
  width: 48px;
  min-width: 48px;
  margin: 0 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.todo-type {
  color: #f56c6c;
}
.todo-date {
  color: #909399;
  // width: 130px;
  // min-width: 130px;
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
  // text-overflow: ellipsis;
  flex: 1;
}
.todo-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.todo-work-type {
  flex: 1;
  font-size: 14px;
  color: #3562db;
  font-weight: 500;
}
.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #909399;
  font-size: 14px;
}
/* 闪烁效果 */
@keyframes flash {
  0% {
    background-color: rgba(53, 98, 219, 0.2);
  }
  50% {
    background-color: rgba(53, 98, 219, 0.5);
  }
  100% {
    background-color: rgba(53, 98, 219, 0.2);
  }
}
.flash-item {
  animation: flash 1s ease-in-out infinite;
}
.status-filter {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding: 0;
  border-radius: 4px;
  flex-wrap: wrap;
}
.status-buttons-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
.status-button {
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: transparent;
  color: #333;
  font-size: 14px;
  transition: all 0.3s;
  border: 1px solid #dcdfe6;
  margin: 0 5px 0 0;
}
.status-button.active {
  background-color: #3562db;
  color: #fff;
  border-color: #3562db;
}
.status-button:hover:not(.active) {
  background-color: #f5f7fa;
}
.timeout-button {
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #f56c6c;
  color: #fff;
  font-size: 14px;
  transition: all 0.3s;
  border: 1px solid #f56c6c;
}
.timeout-button.active {
  background-color: #e64242;
  color: #fff;
  border-color: #e64242;
}
.timeout-button:hover {
  background-color: #e64242;
}
.right-control {
  display: flex;
  align-items: center;
  gap: 10px;
}
.setting-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
}
.expand-icon {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f2f6ff;
  color: #3562db;
  font-size: 14px;
  margin-left: 5px;
  transition: all 0.3s ease;
}
.expand-icon:hover {
  background-color: #3562db;
  color: #fff;
}
.expand-icon i {
  transition: transform 0.3s ease;
}
::v-deep .box-card {
  padding: 15px;
}
::v-deep .box-card .card-body {
  margin-top: 0;
}
/* 通知框样式 */
.notification-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #f56c6c;
  color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  z-index: 99999;
  opacity: 0.9;
  transition: opacity 0.5s ease-in-out;
  min-width: 280px;
}
.notification-content {
  display: flex;
  align-items: center;
}
.notification-icon {
  font-size: 20px;
  margin-right: 10px;
  color: white;
}
.notification-message {
  font-size: 14px;
  font-weight: bold;
}
/* 过渡动画 */
.slide-fade-enter-active {
  transition: all 0.3s ease;
}
.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter, .slide-fade-leave-to /* .slide-fade-leave-active below version 2.1.8 */ {
  transform: translateX(30px);
  opacity: 0;
}
/* 工单详情弹框样式 */
::v-deep .detailDialog {
  .el-dialog__header {
    border-bottom: 1px solid #e6e6e6;
    padding: 14px 20px 12px;
    span {
      font-size: 16px;
    }
  }
  .el-dialog__body {
    height: 70vh;
    overflow-y: auto;
    padding: 15px;
  }
  .el-dialog__headerbtn {
    top: 13px;
    font-size: 18px;
  }
}

/* 自定义列表参数表格拖拽样式 */
::v-deep .setting-body {
  .draggable-row {
    cursor: grab;
    transition: background-color 0.2s ease;
  }

  .draggable-row:hover {
    background-color: #f5f7fa !important;
  }

  .draggable-row:active {
    cursor: grabbing;
  }

  /* 拖拽时的幽灵样式 */
  .sortable-ghost {
    opacity: 0.5;
    background-color: #e6f7ff !important;
    cursor: grabbing;
  }

  /* 拖拽选中时的样式 */
  .sortable-chosen {
    cursor: grabbing;
    background-color: #f0f9ff !important;
  }
}
</style>
