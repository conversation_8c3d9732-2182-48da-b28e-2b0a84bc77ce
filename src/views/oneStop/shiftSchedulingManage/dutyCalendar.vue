<template>
  <PageContainer>
    <div slot="content" class="content">
      <el-row :gutter="16">
        <el-col :span="5" style="height: 100%">
          <div class="leftContent">
            <div style="margin-bottom: 20px; font-size: 18px">值班搜索</div>
            <div class="search_box">
              <el-form :inline="true" :model="formInline" class="demo-form-inline">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="班次" prop="managementId">
                      <el-select v-model="formInline.managementId" placeholder="请选择班次" style="width: 93%" clearable filterable @visible-change="handleSelectVisibleChange">
                        <!-- 如果 options 中的 item.id 可能为 undefined，这里使用索引作为 key -->
                        <el-option v-for="(item, index) in options" :key="index" :label="item.shiftName" :value="item.id"> </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="人员" prop="personnelName">
                      <el-input v-model.trim="formInline.personnelName" placeholder="请输入人员姓名"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div style="text-align: center; margin-top: 20px">
                <el-button class="sino-button-sure white-button" @click="reset">重置</el-button>
                <el-button type="primary" class="sino-button-sure green-button" @click="onSubmit">查询</el-button>
              </div>
              <div class="leftArrClass">
                <div class="title">
                  <i class="wall"></i>
                  <span>排班信息</span>
                </div>
                <div>
                  <div>
                    <!-- 使用索引作为 key -->
                    <div v-for="(item, index) in classes" :key="index">
                      <!-- 渲染班次信息 -->
                      <p class="processeTitle">班次：{{ item.shiftName }}</p>
                      <!-- 遍历每个班次下的时间段和值班人员信息，使用索引作为 key -->
                      <div v-for="(detail, detailIndex) in item.timePeriods" :key="detailIndex" class="timeTileClass">
                        <!-- 根据时间段索引生成合适的时间段名称 -->
                        <p style="margin-bottom: 5px">{{ item.timePeriods.length > 1 ? `时间段${detailIndex + 1}：` : '时间段：' }}{{ detail.timePeriod }}</p>
                        <p>值班人员：{{ getPersonnelNames(detail.personnelDetails) }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="19" style="height: 100%">
          <div class="table_box">
            <div class="custom-calendar">
              <i class="el-icon-d-arrow-left" @click="dateCut(3)"></i>
              <i class="el-icon-arrow-left" @click="dateCut(1)"></i>
              <el-calendar ref="calendar" v-model="calendarVal" :first-day-of-week="7">
                <template #dateCell="{ data }">
                  <div
                    :class="{
                      'has-content': getScheduleForDate(data.day).shiftDetails.length,
                      'clicked-cell': clickedDate === data.day && getScheduleForDate(data.day).shiftDetails.length
                    }"
                    @click="handleCellClick(data.day)"
                  >
                    <!-- 显示日期 -->
                    <span>{{ data.day.split('-').pop() }}</span>
                    <!-- 循环显示该日期对应的事件 -->
                    <div
                      :ref="`eventContainer_${data.day}`"
                      class="event-container"
                      :style="{ maxHeight: maxHeight + 'px' }"
                      :class="{ 'clicked-cell': clickedDate === data.day && getScheduleForDate(data.day).shiftDetails.length }"
                    >
                      <!-- 如果 shift.shiftName 可能重复，使用索引作为 key -->
                      <div v-for="(shift, shiftIndex) in getScheduleForDate(data.day).shiftDetails" :key="shiftIndex" class="event-item">
                        {{ shift.shiftName }}:
                        <!-- 如果 item.timePeriod 可能重复，使用索引作为 key -->
                        <span v-for="(item, itemIndex) in shift.timePeriods" :key="itemIndex">
                          {{ item.timePeriod + '&nbsp;' }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div v-if="getScheduleForDate(data.day).shiftDetails.length" class="moreDiv">
                    <el-popover placement="top-start" :title="data.day + weekdayName" width="200" trigger="click">
                      <!-- 如果 shift 可能重复，使用索引作为 key -->
                      <div v-for="(shift, shiftIndex) in getScheduleForDate(data.day).shiftDetails" :key="shiftIndex" class="event-item">
                        {{ shift.shiftName }}{{ ':' }}
                        <!-- 如果 item 可能重复，使用索引作为 key -->
                        <span v-for="(item, itemIndex) in shift.timePeriods" :key="itemIndex">
                          {{ item.timePeriod + '&nbsp;' }}
                        </span>
                      </div>
                      <el-button v-show="shouldShowMore[data.day]" slot="reference" type="text" class="moreClass" @click="moreClick(data.day, $event)">更多</el-button>
                    </el-popover>
                    <div type="text" style="height: 24px; background: #dcf3fd"></div>
                  </div>
                </template>
              </el-calendar>
              <i class="el-icon-arrow-right" @click="dateCut(2)"></i>
              <i class="el-icon-d-arrow-right" @click="dateCut(4)"></i>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </PageContainer>
</template>
<script>
import Vue from 'vue'
export default {
  name: 'dutyCalendar',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      classes: [],
      visible: false,
      events: [],
      calendarVal: new Date(),
      weekdayName: '',
      formInline: {
        personnelName: '',
        managementId: ''
      },
      cellHeightsInfo: {},
      options: [],
      newCalendarArr: [],
      currentYearMonth: '',
      paginationConfig: {
        currentPage: 1,
        pageSize: 999,
        total: 0
      },
      maxHeight: 80, // 最大显示高度
      shouldShowMore: {},
      clickedDate: null
    }
  },
  watch: {
    calendarVal: {
      handler(val) {
        // 创建 Date 对象
        const date = new Date(val)
        // 获取年份
        const year = date.getFullYear()
        // 获取月份，注意 getMonth() 返回值是 0 - 11，所以要加 1
        const month = String(date.getMonth() + 1).padStart(2, '0')
        // 格式化成年月形式
        const formattedDate = `${year}-${month}`
        this.$api.oneStopApi
          .getSchedulingCalendar({
            queryDate: formattedDate,
            personnelName: this.formInline.personnelName,
            managementId: this.formInline.managementId
          })
          .then((res) => {
            this.events = res.data
            this.events.forEach((item) => {
              if (!item.shiftDetails) {
                item.shiftDetails = []
              }
              this.isShowMore(item.date, (result) => {
                Vue.set(this.shouldShowMore, item.date, result)
              })
            })
          })
      },
      immediate: true // 立即执行
    }
  },
  created() {},
  mounted() {
    this.getManagementList()
   // this.dateCut(5)
  },
  methods: {
    // 下拉框点击
    handleSelectVisibleChange(visible) {
      if (visible) {
        this.getManagementList()
      } else {
        //  this.getManagementList()
      }
    },
    isShowMore(date, callback) {
      this.$nextTick(() => {
        const eventContainer = this.$refs[`eventContainer_${date}`]
        if (eventContainer) {
          const contentHeight = eventContainer.scrollHeight
          const result = contentHeight > this.maxHeight
          callback(result)
        } else {
          callback(false)
        }
      })
    },
    // 值班人员处理
    getPersonnelNames(personnelDetails) {
      if (!personnelDetails || personnelDetails.length === 0) {
        return '无'
      }
      return personnelDetails
        .map((person) => {
          if (person.personnelPhone) {
            return `${person.personnelName}-${person.personnelPhone}`
          }
          return person.personnelName
        })
        .join(', ')
    },
    // 获取班次信息
    getManagementList() {
      const { currentPage, pageSize } = this.paginationConfig
      let params = {
        pageNo: currentPage,
        pageSize
      }
      this.$api.oneStopApi.queryShiiftByPage(params).then((res) => {
        if (res.code === 200) {
          this.options = res.data.list
        }
      })
    },
    // 查询日历信息
    getSchedulingCalendar() {
      this.$api.oneStopApi
        .getSchedulingCalendar({
          queryDate: this.calendarVal,
          personnelName: this.formInline.personnelName,
          managementId: this.formInline.managementId
        })
        .then((res) => {
          this.events = res.data
          this.events.forEach((item) => {
            if (!item.shiftDetails) {
              item.shiftDetails = []
            }
            this.isShowMore(item.date, (result) => {
              Vue.set(this.shouldShowMore, item.date, result)
            })
          })
        })
    },
    // 处理日历数据
    getScheduleForDate(date) {
      const schedule = this.events.find((item) => item.date == date)
      return (
        schedule || {
          shiftDetails: []
        }
      )
    },
    // //循环日历数据
    // getSchedule(date) {
    //   const schedule = this.events.find(item => item.date == date);
    //   const scheduleData = schedule.shiftDetails.find((item) => {
    //     return item.timePeriods
    //   })
    //   return scheduleData.timePeriods.length
    // },
    // 点击更多
    moreClick(date, event) {
      this.newCalendarArr = this.events.filter((item) => {
        return date == item.date
      })
      this.getWeek(date)
    },
    // 获取星期
    getWeek(dateWeek) {
      // 创建一个 Date 对象，传入指定日期
      const date = new Date(dateWeek)
      // 获取星期几，返回值是 0（星期日）到 6（星期六）的整数
      const dayOfWeek = date.getDay()
      // 定义一个包含星期名称的数组
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      // 根据获取的数字索引从数组中取出对应的星期名称
      this.weekdayName = weekdays[dayOfWeek]
    },
    // 日期单元格点击事件
    handleCellClick(date) {
      this.clickedDate = date
      this.$api.oneStopApi
        .getSpecificDaySchedule({
          specificDay: date,
          personnelName: this.formInline.personnelName,
          managementId: this.formInline.managementId
        })
        .then((res) => {
          this.classes = res.data
        })
    },
    dateCut(type) {
      // type：1 月份左侧按钮，2 月份右侧按钮，3 年份左侧按钮，4 年份右侧按钮
      if (type === 1) {
        this.calendarVal =
          new Date(this.calendarVal).getMonth() > 0
            ? new Date(this.calendarVal).getFullYear() + '-' + String(new Date(this.calendarVal).getMonth()).padStart(2, '0')
            : new Date(this.calendarVal).getFullYear() - 1 + '-12'
      } else if (type === 2) {
        this.calendarVal =
          new Date(this.calendarVal).getMonth() < 11
            ? new Date(this.calendarVal).getFullYear() + '-' + String(Number(new Date(this.calendarVal).getMonth()) + 2).padStart(2, '0')
            : new Date(this.calendarVal).getFullYear() + 1 + '-01'
      } else if (type === 3) {
        this.calendarVal = new Date(this.calendarVal).getFullYear() - 1 + '-' + String(Number(new Date(this.calendarVal).getMonth()) + 1).padStart(2, '0')
      } else if (type === 4) {
        this.calendarVal = new Date(this.calendarVal).getFullYear() + 1 + '-' + String(Number(new Date(this.calendarVal).getMonth()) + 1).padStart(2, '0')
      } else if (type === 5) {
        const currentYear = new Date().getFullYear()
        const currentMonth = new Date().getMonth() + 1
        const formattedMonth = String(currentMonth).padStart(2, '0')
        this.calendarVal = currentYear + '-' + formattedMonth
      }
      //this.getSchedulingCalendar() // 调接口数据
    },
    // 重置
    reset() {
      this.formInline.personnelName = ''
      this.formInline.managementId = ''
      this.classes = []
      this.dateCut(5)
      this.clickedDate = null
    },
    // 查询
    onSubmit() {
      const date = new Date(this.calendarVal)
      // 获取年份
      const year = date.getFullYear()
      // 获取月份，注意 getMonth() 返回值从 0 开始，所以要加 1
      let month = date.getMonth() + 1
      // 给月份添加前导零，确保是两位数
      month = month.toString().padStart(2, '0')
      const formattedDate = `${year}-${month}`
      this.calendarVal = formattedDate
      this.getSchedulingCalendar()
      this.classes = []
      this.clickedDate = null
    }
  }
}
</script>
<style scoped lang="scss">
.content {
  height: 100%;
  display: flex;
}
.leftContent {
  border-radius: 6px;
  height: 100%;
  padding: 16px;
  background-color: #fff;
  overflow: auto;
}
.table_box {
  border-radius: 6px;
  height: 100%;
  background-color: #fff;
  padding: 12px 16px;
  overflow: hidden;
}
.custom-calendar {
  position: relative;
  height: 100%;
  overflow: auto;
}
.custom-calendar i {
  position: absolute;
  top: 2.5%;
  transform: translateY(-50%);
  cursor: pointer;
}
.custom-calendar .el-icon-arrow-left {
  left: 45%;
  /* 根据需要调整位置 */
}
.custom-calendar .el-icon-arrow-right {
  right: 45%;
  /* 根据需要调整位置 */
}
.custom-calendar .el-icon-d-arrow-left {
  left: 44%;
  /* 根据需要调整位置 */
}
.custom-calendar .el-icon-d-arrow-right {
  right: 44%;
  /* 根据需要调整位置 */
}
.event-item {
  font-size: 16px;
  max-height: 100px;
  margin-bottom: 8px;
  overflow: hidden;
  /* 当内容超出时显示滚动条 */
}
.has-content {
  background-color: #dcf3fd;
  height: 87%;
  overflow: hidden;
  width: 100%;
}
.clicked-cell {
  color: #ff9435; /* 改变点击后格子的颜色 */
}
.moreDiv {
  background-color: #dcf3fd;
}
.moreClass {
  padding-left: 0px;
  font-size: 14px;
}
.leftArrClass {
  margin-top: 20px;
}
.title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
}
.title > span {
  font-weight: normal;
  color: #303133;
}
.wall {
  display: inline-block;
  width: 4px;
  height: 20px;
  background-color: #38c7c4;
  margin-right: 8px;
  vertical-align: middle;
  transform: translateY(-2px);
}
.processeTitle {
  font-weight: 700;
  margin-bottom: 10px;
}
.timeTileClass {
  margin-bottom: 10px;
}
/*.event-container {
    overflow: hidden;
    transition: max-height 0.3s ease;
  } */
::v-deep .el-calendar {
  height: 100%;
}
::v-deep .el-button-group {
  display: none !important;
}
::v-deep .el-calendar__header {
  justify-content: center !important;
}
::v-deep .el-calendar-table {
  height: 100% !important;
}
::v-deep .el-calendar-table .el-calendar-day {
  padding: 5px 5px 16px 5px;
  height: 100px;
  /* padding-bottom: 8px; */
  /* 超出宽度的内容隐藏 */
  overflow: hidden;
  word-break: break-all;
}
::v-deep .el-calendar__body {
  width: 100%;
  padding: 12px 20px 20px;
  height: calc(100% - 60px);
}
.el-calendar-table {
  height: 100%;
}
</style>
